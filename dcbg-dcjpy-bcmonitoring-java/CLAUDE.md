# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Java 21 Spring Boot blockchain monitoring application that monitors Ethereum events and stores them in AWS services (DynamoDB, S3, SQS). It follows Clean Architecture principles with clear separation between domain, application, and infrastructure layers.

## Essential Commands

### Building and Testing
```bash
# Code formatting and static analysis
./gradlew spotlessGroovyGradleApply

# Build project
./gradlew build

# Run unit tests (fast, mock-based)
./gradlew test

# Run integration tests with TestContainers (requires Docker)
./gradlew testAdhoc

# Run specific test class
./gradlew test --tests "*MonitorEventServiceSpec"

# Generate test coverage report
./gradlew jacocoTestReport
```

### Docker Operations
```bash
# Build Docker image
docker build -t dcbg-dcjpy-bcmonitoring:latest .

# Start with Docker Compose (includes LocalStack)
docker-compose up -d

# Local development with LocalStack
docker-compose -f docker-compose-local.yml up -d
```

## Architecture Overview

### Layer Structure
- **Controller**: REST endpoints (`DiagnosticController` for health checks)
- **Application**: Business logic services (`MonitorEventService`, `DownloadAbiService`)
- **Domain**: Core models (`Event`, `Transaction`, `BlockHeight`, `ContractInfo`)
- **Infrastructure**: External integrations (DynamoDB, S3, Ethereum via Web3j)
- **Config**: Centralized configuration management

### Key Services
- **MonitorEventService**: Core blockchain monitoring using Web3j WebSocket subscriptions
- **DownloadAbiService**: Loads smart contract ABIs from S3 for event parsing
- **EthEventLogDao**: Ethereum integration with connection pooling and error handling
- **AbiParser**: Parses contract ABI files (supports Hardhat/Truffle formats)

### Data Flow
1. Application starts → Download ABIs from S3 → Start blockchain monitoring
2. Monitor Ethereum via WebSocket → Parse events using ABI definitions → Store in DynamoDB
3. Processes both pending and new transactions concurrently using virtual threads

## Test Architecture

### Test Types
- **Unit Tests** (`src/test/groovy/com/...`): Fast Spock-based tests with mocks
- **Adhoc Tests** (`src/test/groovy/adhoc/...`): Integration tests with TestContainers and LocalStack

### Test Utilities
- **AdhocHelper**: Comprehensive test utility for Docker container management, DynamoDB setup, S3 operations
- **TestContainers**: Real AWS services via LocalStack for integration testing
- Test data located in `docker/local/data/s3/` directory

### Running Tests
- Regular tests exclude adhoc/** (fast execution)
- Adhoc tests exclude com/** (full integration with real services)
- Both test types use Groovy + Spock framework

## Configuration Management

### Key Configuration Classes
- **BcmonitoringConfigurationProperties**: Central config with nested AWS/Ethereum settings
- **Web3jConfig**: WebSocket connection management with basic error handling
- **MonitoringRunnerConfig**: Simple monitoring loop with exception handling and restart logic
- **EnvironmentConfigManager**: Utility for environment-based configuration management

### Environment-Based Configuration
The application supports multiple environments with dedicated configuration files:

- **Local** (`application-local.properties`): LocalStack for local development
- **Dev** (`application-dev.properties`): Real AWS services with dev settings
- **Test** (`application-test.properties`): LocalStack optimized for testing
- **Prod** (`application-prod.properties`): Production AWS services

### Running Tests with Specific Environment
```bash
# Using provided script
./scripts/run-tests-with-environment.sh dev
./scripts/run-tests-with-environment.sh local ConfigurationServiceITSpec

# Using Gradle directly
./gradlew testAdhoc -Dtest.environment=dev
TEST_ENVIRONMENT=dev ./gradlew testAdhoc
```

### Environment Support
- Local development with LocalStack (docker-compose-local.yml)
- Test environment with TestContainers
- Development environment with real AWS services
- Production-ready AWS service integration
- Environment variable overrides (DEV_*, PROD_* prefixes)

## Resilience Patterns

The application implements several resilience patterns:
- **Circuit Breaker**: Web3j connection management with automatic recovery
- **Simple Restart Pattern**: Basic exception handling with monitoring loop restart
- **Resource Management**: Proper Web3j instance lifecycle to prevent connection leaks
- **Bulk Processing**: Separate handling of pending vs new transactions

## Development Notes

### Adding New Event Types
1. Update contract ABI files in S3 (or `docker/local/data/s3/` for local testing)
2. Events are automatically parsed using the AbiParser
3. No code changes needed for new event signatures

### Database Schema
- **Events Table**: Composite key (transactionHash + logIndex)
- **BlockHeight Table**: Tracks processing progress (id + blockNumber)

### Logging
- Structured logging with correlation IDs
- Environment-specific log levels
- Comprehensive error context for blockchain operations

### Current Development
Working on startup service implementation with adhoc test integration (branch: `featute/DCPF-44953-local-implementation-start-service`).