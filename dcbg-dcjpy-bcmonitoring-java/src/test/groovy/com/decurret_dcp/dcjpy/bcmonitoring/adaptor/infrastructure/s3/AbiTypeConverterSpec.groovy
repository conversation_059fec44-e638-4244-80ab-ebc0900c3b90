package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import com.decurret_dcp.dcjpy.bcmonitoring.exception.UnsupportedTypeException
import org.web3j.abi.datatypes.*
import org.web3j.abi.datatypes.generated.*
import org.web3j.abi.datatypes.primitive.*
import spock.lang.Specification

class AbiTypeConverterSpec extends Specification {

    def "should convert Solidity type '#solidityType' with indexed=#indexed to #expectedClass"() {
        when:
        def result = AbiTypeConverter.convertType(solidityType, indexed, Collections.emptyList())

        then:
        result.getType() == expectedClass
        result.isIndexed() == indexed

        where:
        solidityType | indexed || expectedClass

        // Basic types
        "bool"       | true    || Bool.class
        "boolean"    | true    || Bool.class
        "address"    | false   || Address.class
        "string"     | false   || Utf8String.class

        // Primitive Java types
        "double"     | false   || Double.class
        "float"      | false   || Float.class
        "char"       | false   || Char.class
        "short"      | false   || Short.class
        "long"       | false   || Long.class
        "byte"       | true    || Byte.class

        // Bytes types
        "bytes"      | false   || DynamicBytes.class
        "bytes1"     | false   || Bytes1.class
        "bytes2"     | false   || Bytes2.class
        "bytes3"     | false   || Bytes3.class
        "bytes4"     | false   || Bytes4.class
        "bytes5"     | false   || Bytes5.class
        "bytes6"     | false   || Bytes6.class
        "bytes7"     | false   || Bytes7.class
        "bytes8"     | false   || Bytes8.class
        "bytes9"     | false   || Bytes9.class
        "bytes10"    | false   || Bytes10.class
        "bytes11"    | false   || Bytes11.class
        "bytes12"    | false   || Bytes12.class
        "bytes13"    | false   || Bytes13.class
        "bytes14"    | false   || Bytes14.class
        "bytes15"    | false   || Bytes15.class
        "bytes16"    | false   || Bytes16.class
        "bytes17"    | false   || Bytes17.class
        "bytes18"    | false   || Bytes18.class
        "bytes19"    | false   || Bytes19.class
        "bytes20"    | false   || Bytes20.class
        "bytes21"    | false   || Bytes21.class
        "bytes22"    | false   || Bytes22.class
        "bytes23"    | false   || Bytes23.class
        "bytes24"    | false   || Bytes24.class
        "bytes25"    | false   || Bytes25.class
        "bytes26"    | false   || Bytes26.class
        "bytes27"    | false   || Bytes27.class
        "bytes28"    | false   || Bytes28.class
        "bytes29"    | false   || Bytes29.class
        "bytes30"    | false   || Bytes30.class
        "bytes31"    | false   || Bytes31.class
        "bytes32"    | false   || Bytes32.class

        // Web3j uint types
        "uint"       | false   || Uint.class
        "uint8"      | false   || Uint8.class
        "uint16"     | false   || Uint16.class
        "uint24"     | false   || Uint24.class
        "uint32"     | false   || Uint32.class
        "uint40"     | false   || Uint40.class
        "uint48"     | false   || Uint48.class
        "uint56"     | false   || Uint56.class
        "uint64"     | false   || Uint64.class
        "uint72"     | false   || Uint72.class
        "uint80"     | false   || Uint80.class
        "uint88"     | false   || Uint88.class
        "uint96"     | false   || Uint96.class
        "uint104"    | false   || Uint104.class
        "uint112"    | false   || Uint112.class
        "uint120"    | false   || Uint120.class
        "uint128"    | false   || Uint128.class
        "uint136"    | false   || Uint136.class
        "uint144"    | false   || Uint144.class
        "uint152"    | false   || Uint152.class
        "uint160"    | false   || Uint160.class
        "uint168"    | false   || Uint168.class
        "uint176"    | false   || Uint176.class
        "uint184"    | false   || Uint184.class
        "uint192"    | false   || Uint192.class
        "uint200"    | false   || Uint200.class
        "uint208"    | false   || Uint208.class
        "uint216"    | false   || Uint216.class
        "uint224"    | false   || Uint224.class
        "uint232"    | false   || Uint232.class
        "uint240"    | false   || Uint240.class
        "uint248"    | false   || Uint248.class
        "uint256"    | false   || Uint256.class

        // Web3j int types
        "int8"       | false   || Int8.class
        "int16"      | false   || Int16.class
        "int24"      | false   || Int24.class
        "int32"      | false   || Int32.class
        "int40"      | false   || Int40.class
        "int48"      | false   || Int48.class
        "int56"      | false   || Int56.class
        "int64"      | false   || Int64.class
        "int72"      | false   || Int72.class
        "int80"      | false   || Int80.class
        "int88"      | false   || Int88.class
        "int96"      | false   || Int96.class
        "int104"     | false   || Int104.class
        "int112"     | false   || Int112.class
        "int120"     | false   || Int120.class
        "int128"     | false   || Int128.class
        "int136"     | false   || Int136.class
        "int144"     | false   || Int144.class
        "int152"     | false   || Int152.class
        "int160"     | false   || Int160.class
        "int168"     | false   || Int168.class
        "int176"     | false   || Int176.class
        "int184"     | false   || Int184.class
        "int192"     | false   || Int192.class
        "int200"     | false   || Int200.class
        "int208"     | false   || Int208.class
        "int216"     | false   || Int216.class
        "int224"     | false   || Int224.class
        "int232"     | false   || Int232.class
        "int240"     | false   || Int240.class
        "int248"     | false   || Int248.class
        "int256"     | false   || Int256.class
    }

    def "should handle unsupported type gracefully"() {
        when:
        AbiTypeConverter.convertType("unsupportedType", false, Collections.emptyList())

        then:
        def ex = thrown(UnsupportedTypeException)
        ex.message.contains("Error creating dynamic struct")
    }

    def "should handle tuple type with components"() {
        given: "Components for a tuple"
        def components = [
            createNamedType("field1", "uint256", null),
            createNamedType("field2", "address", null)
        ]

        when: "Converting tuple type"
        def result = AbiTypeConverter.convertType("tuple", false, components)

        then: "Should create TypeReference for struct"
        result != null
        result.isIndexed() == false
    }

    def "should handle tuple array type with components"() {
        given: "Components for a tuple array"
        def components = [
            createNamedType("field1", "uint256", null),
            createNamedType("field2", "address", null)
        ]

        when: "Converting tuple array type"
        def result = AbiTypeConverter.convertType("tuple[]", false, components)

        then: "Should create TypeReference for dynamic array of structs"
        result != null
        result.isIndexed() == false
    }

    def "should handle dynamic array type"() {
        when: "Converting dynamic array type"
        def result = AbiTypeConverter.convertType("uint256[]", false, null)

        then: "Should create TypeReference for dynamic array"
        result != null
        result.isIndexed() == false
    }

    def "should throw exception for unsupported array element type"() {
        when: "Converting array with unsupported element type"
        AbiTypeConverter.convertType("unsupportedElement[]", false, null)

        then: "Should throw UnsupportedTypeException"
        thrown(UnsupportedTypeException)
    }

    def "should handle nested tuple components"() {
        given: "Nested tuple components"
        def nestedComponents = [
            createNamedType("nestedField", "uint256", null)
        ]
        def components = [
            createNamedType("field1", "uint256", null),
            createNamedType("field2", "tuple", nestedComponents)
        ]

        when: "Converting tuple with nested components"
        def result = AbiTypeConverter.convertType("tuple", false, components)

        then: "Should handle nested tuples"
        result != null
        result.isIndexed() == false
    }

    def "should throw exception for unsupported nested component type"() {
        given: "Components with unsupported nested type"
        def components = [
            createNamedType("field1", "uint256", null),
            createNamedType("field2", "unsupportedNestedType", null)
        ]

        when: "Converting tuple with unsupported nested type"
        AbiTypeConverter.convertType("tuple", false, components)

        then: "Should throw UnsupportedTypeException"
        thrown(UnsupportedTypeException)
    }

    def "constructor should be callable for completeness"() {
        when: "Creating an instance of AbiTypeConverter"
        def converter = new AbiTypeConverter()

        then: "Should create instance successfully"
        converter != null
        converter instanceof AbiTypeConverter
    }

    def "should handle tuple type with null components"() {
        when: "Converting tuple type with null components"
        AbiTypeConverter.convertType("tuple", false, null)

        then: "Should throw UnsupportedTypeException"
        thrown(UnsupportedTypeException)
    }

    def "should handle tuple type with empty components"() {
        when: "Converting tuple type with empty components"
        def result = AbiTypeConverter.convertType("tuple", false, [])

        then: "Should create TypeReference successfully"
        result != null
        result.isIndexed() == false
    }

    def "should handle non-tuple type with null components"() {
        when: "Converting non-tuple type with null components"
        AbiTypeConverter.convertType("customType", false, null)

        then: "Should throw UnsupportedTypeException"
        thrown(UnsupportedTypeException)
    }

    def "should handle non-tuple type with components"() {
        given: "Components for non-tuple type"
        def components = [
            createNamedType("field1", "uint256", null)
        ]

        when: "Converting non-tuple type with components"
        AbiTypeConverter.convertType("customType", false, components)

        then: "Should throw UnsupportedTypeException"
        thrown(UnsupportedTypeException)
    }

    def "should handle tuple type with null components in resolveComponentType"() {
        when: "Resolving tuple type with null components"
        AbiTypeConverter.resolveComponentType("tuple", null)

        then: "Should throw UnsupportedTypeException"
        def exception = thrown(UnsupportedTypeException)
        exception.message == "Unsupported type: tuple"
    }

    def "should handle tuple type with empty components in resolveComponentType"() {
        when: "Resolving tuple type with empty components"
        AbiTypeConverter.resolveComponentType("tuple", [])

        then: "Should throw UnsupportedTypeException"
        def exception = thrown(UnsupportedTypeException)
        exception.message == "Unsupported type: tuple with 0 components"
    }

    def "should call getType method in dynamic array TypeReference"() {
        when: "Creating dynamic array TypeReference and calling getType"
        def typeRef = AbiTypeConverter.extractTypeReferenceDynamicArray(org.web3j.abi.datatypes.generated.Uint256.class)
        def type = typeRef.getType()

        then: "Should return the correct parameterized type"
        type != null
        // The type should be a ParameterizedType with DynamicArray as raw type
        type instanceof java.lang.reflect.ParameterizedType
        ((java.lang.reflect.ParameterizedType) type).getRawType() == org.web3j.abi.datatypes.DynamicArray.class
    }

    private createNamedType(String name, String type, List components) {
        def namedType = Mock(org.web3j.protocol.core.methods.response.AbiDefinition.NamedType)
        namedType.getName() >> name
        namedType.getType() >> type
        namedType.getComponents() >> components
        return namedType
    }
}
