package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import java.lang.reflect.Type
import spock.lang.Specification

class ParameterizedTypeImplSpec extends Specification {

    def "constructor should set rawType and typeArguments"() {
        given: "Raw type and type arguments"
        def rawType = List.class
        def typeArguments = [String.class, Integer.class] as Type[]

        when: "Creating ParameterizedTypeImpl"
        def parameterizedType = new ParameterizedTypeImpl(rawType, typeArguments)

        then: "Should set fields correctly"
        parameterizedType.getRawType() == rawType
        parameterizedType.getActualTypeArguments() == typeArguments
    }

    def "constructor should handle single type argument"() {
        given: "Raw type and single type argument"
        def rawType = List.class
        def typeArgument = String.class

        when: "Creating ParameterizedTypeImpl with single argument"
        def parameterizedType = new ParameterizedTypeImpl(rawType, typeArgument)

        then: "Should set fields correctly"
        parameterizedType.getRawType() == rawType
        parameterizedType.getActualTypeArguments().length == 1
        parameterizedType.getActualTypeArguments()[0] == typeArgument
    }

    def "constructor should handle no type arguments"() {
        given: "Raw type with no type arguments"
        def rawType = List.class

        when: "Creating ParameterizedTypeImpl with no arguments"
        def parameterizedType = new ParameterizedTypeImpl(rawType)

        then: "Should set fields correctly"
        parameterizedType.getRawType() == rawType
        parameterizedType.getActualTypeArguments().length == 0
    }

    def "getActualTypeArguments should return type arguments array"() {
        given: "ParameterizedTypeImpl with type arguments"
        def typeArguments = [String.class, Integer.class] as Type[]
        def parameterizedType = new ParameterizedTypeImpl(List.class, typeArguments)

        when: "Getting actual type arguments"
        def result = parameterizedType.getActualTypeArguments()

        then: "Should return the same array"
        result == typeArguments
        result.length == 2
        result[0] == String.class
        result[1] == Integer.class
    }

    def "getRawType should return raw type"() {
        given: "ParameterizedTypeImpl with raw type"
        def rawType = Map.class
        def parameterizedType = new ParameterizedTypeImpl(rawType, String.class, Integer.class)

        when: "Getting raw type"
        def result = parameterizedType.getRawType()

        then: "Should return the raw type"
        result == rawType
    }

    def "getOwnerType should always return null"() {
        given: "ParameterizedTypeImpl"
        def parameterizedType = new ParameterizedTypeImpl(List.class, String.class)

        when: "Getting owner type"
        def result = parameterizedType.getOwnerType()

        then: "Should return null"
        result == null
    }

    def "should handle null raw type"() {
        given: "Null raw type"
        def rawType = null
        def typeArguments = [String.class] as Type[]

        when: "Creating ParameterizedTypeImpl with null raw type"
        def parameterizedType = new ParameterizedTypeImpl(rawType, typeArguments)

        then: "Should handle null gracefully"
        parameterizedType.getRawType() == null
        parameterizedType.getActualTypeArguments() == typeArguments
    }

    def "should handle null type arguments"() {
        given: "Raw type and null type arguments"
        def rawType = List.class
        Type[] typeArguments = null

        when: "Creating ParameterizedTypeImpl with null type arguments"
        def parameterizedType = new ParameterizedTypeImpl(rawType, typeArguments)

        then: "Should handle null gracefully"
        parameterizedType.getRawType() == rawType
        parameterizedType.getActualTypeArguments() == null
    }

    def "should work with complex generic types"() {
        given: "Complex generic types"
        def rawType = Map.class
        def keyType = String.class
        def valueType = List.class

        when: "Creating ParameterizedTypeImpl for Map<String, List>"
        def parameterizedType = new ParameterizedTypeImpl(rawType, keyType, valueType)

        then: "Should handle complex types correctly"
        parameterizedType.getRawType() == rawType
        parameterizedType.getActualTypeArguments().length == 2
        parameterizedType.getActualTypeArguments()[0] == keyType
        parameterizedType.getActualTypeArguments()[1] == valueType
    }

    def "should be usable as ParameterizedType interface"() {
        given: "ParameterizedTypeImpl instance"
        def parameterizedType = new ParameterizedTypeImpl(List.class, String.class)

        when: "Using as ParameterizedType interface"
        java.lang.reflect.ParameterizedType type = parameterizedType

        then: "Should work as expected"
        type.getRawType() == List.class
        type.getActualTypeArguments().length == 1
        type.getActualTypeArguments()[0] == String.class
        type.getOwnerType() == null
    }
}
