package adhoc.error_handling

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.doAnswer

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Level
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.web3j.protocol.websocket.events.NewHeadsNotification

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class ErrorHandlingAndRecoveryITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Autowired
	BcmonitoringConfigurationProperties properties

	@Autowired
	AbiParser abiParser

	@MockitoSpyBean
	BlockHeightDao blockHeightDao

	@MockitoSpyBean
	EventDao eventDao

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"AccessCtrl",
			"Token",
			"Account",
			"Provider"
		])
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should restart when websocket disconnect
	 * Expected: Service retries each 3 seconds delays (effectively instant), reinitialized monitor on retry
	 * Service logs “Restart bc monitoring”
	 */
	def "Should restart when websocket disconnect"() {
		given: "Websocket disconnected"
		// Create a Flowable that immediately emits an error to simulate a WebSocket disconnect
		def disconnectFlowable = Flowable.defer({
			def processor = PublishProcessor.<NewHeadsNotification>create()
			processor.onError(new RuntimeException("WebSocket connection lost"))

			return processor
		})

		// Setup mock Web3j to return the disconnect flowable
		web3j.newHeadsNotifications() >>  disconnectFlowable

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Service logs Restarting bc monitoring"
		def messages = logAppender.list*.formattedMessage
		assert messages.any { it.contains("Subscription error") }
		assert messages.any { it.contains("Error in monitoring: Error subscribing to blockchain events") }
		assert messages.any { it.contains("Restarting bc monitoring") }

		and: "Interval restart approximately 3s"
		// Filter log entries that contain "Restarting bc monitoring"
		def restartLogs = logAppender.list.findAll {
			it.formattedMessage.contains("Restarting bc monitoring")
		}

		// Extract timestamps from the matched log entries
		def restartTimestamps = restartLogs*.timeStamp
		// Ensure we have at least two entries to calculate intervals
		assert restartTimestamps.size() >= 2

		// Pair timestamps to calculate intervals between consecutive logs
		def intervals = restartTimestamps.collate(2, 1, false).collect {
			it[1] - it[0]
		}
		// Verify that each interval is approximately 3 seconds (±200ms tolerance)
		intervals.eachWithIndex { interval, i ->
			assert interval >= 2800 && interval <= 3200 : "Interval at index ${i} is ${interval}ms, expected ~3s"
		}
	}

	/**
	 * Should continues operation when non-critical errors occur
	 * Expected: Blocks delayed, monitoring continues process events
	 * Service logs: Block %d is delay by more than %d seconds
	 * Level log: WARN
	 */
	def "Should continues operation when non-critical errors occur"() {
		given: "Blocks and Events"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		// Setup pending events: 2 role-related events from earlier block
		def pendingEvents = ["roleGranted", "roleRevoked"]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Log block is delayed"
		assert logAppender.list.any {
			it.formattedMessage =~ /Block \d+ is delayed by more than \d+ seconds/ && it.level.toString() == "WARN"
		}

		and: "Service not stop and restart"
		assert logAppender.list.formattedMessage.any { !it.contains("Restarting bc monitoring") }
	}

	/**
	 * Service retry when block number is zero of pending transaction
	 * Expected: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)
	 * Service logs: Error in monitoring loop:
	 */
	def "Service retry when block number is zero of pending transaction"() {
		given: "Pending event with block number is zero"
		// Mock event stream for avoid subscribe to blockchain events error
		setUpEventStream(Collections.emptyList())

		// Setup mock pending events with 1 block number zero
		def mockPendingEventLogs1 = createMockPendingEventLogs([
			"roleGranted",
			"roleAdminChanged"
		], 0L, "0xabc100")
		// Setup mock pending events with 1 block number valid
		def mockPendingEventLogs2 = createMockPendingEventLogs(["roleRevoked"], 101L, "0xabc101")
		def combinedPendingEventLogs = mockPendingEventLogs1 + mockPendingEventLogs2
		setUpPendingEvent(combinedPendingEventLogs)

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 20 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		and: "Service logs: Block height Number is zero"
		assert logAppender.list.any {
			it.formattedMessage.contains("Block height Number is zero") && it.level == Level.ERROR
		}

		and: "Service logs: Error in monitoring loop:"
		assert logAppender.list.any {
			it.formattedMessage.contains("Error in monitoring loop:") && it.level == Level.ERROR
		}

		and: "Service restart interval ~3s"
		assert logAppender.list.any { it.formattedMessage.contains("Restarting bc monitoring") }
		// Filter log entries that contain "Restarting bc monitoring"
		def restartLogs = logAppender.list.findAll {
			it.formattedMessage.contains("Restarting bc monitoring")
		}

		// Extract timestamps from the matched log entries
		def restartTimestamps = restartLogs*.timeStamp
		// Ensure we have at least two entries to calculate intervals
		assert restartTimestamps.size() >= 2

		// Pair timestamps to calculate intervals between consecutive logs
		def intervals = restartTimestamps.collate(2, 1, false).collect {
			it[1] - it[0]
		}
		// Verify that each interval is approximately 3 seconds (±200ms tolerance)
		intervals.eachWithIndex { interval, i ->
			assert interval >= 2800 && interval <= 3200 : "Interval at index ${i} is ${interval}ms, expected ~3s"
		}
	}

	/**
	 * Should retry when save block height of pending transaction error
	 * Expected: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)
	 * Service logs: Error in monitoring loop:
	 */
	def "Should retry when save block height of pending transaction error" () {
		given: "Pending events and BlockHeight valid"
		// Mock event stream for avoid subscribe error
		setUpEventStream(Collections.emptyList())

		// Setup mock pending events
		def mockPendingEventLogs1 = createMockPendingEventLogs([
			"roleGranted",
			"roleAdminChanged"
		], 100L, "0xabc100")
		def mockPendingEventLogs2 = createMockPendingEventLogs(["roleRevoked"], 101L, "0xabc101")
		def combinedPendingEventLogs = mockPendingEventLogs1 + mockPendingEventLogs2
		setUpPendingEvent(combinedPendingEventLogs)

		// Mock the 'save' method to return false for the first two calls
		def saveCallCount = new AtomicInteger(0)

		doAnswer { invocation ->
			if (saveCallCount.incrementAndGet() <= 2) {
				return false // Mock 2 times call : false
			} else {
				return invocation.callRealMethod()
			}
		}.when(blockHeightDao).save(any(BlockHeight))

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 15 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		def messages = logAppender.list


		and: "Service logs: Failed to save block height"
		assert messages.any {
			it.formattedMessage.contains("Failed to save block height") && it.level == Level.ERROR
		}

		and: "Service logs: Error in monitoring loop:"
		assert messages.any {
			it.formattedMessage.contains("Error in monitoring loop:") && it.level == Level.ERROR
		}

		and: "Service restart interval ~3s"
		assert messages.formattedMessage.any { it.contains("Restarting bc monitoring") }
		// Filter log entries that contain "Restarting bc monitoring"
		def restartLogs = logAppender.list.findAll {
			it.formattedMessage.contains("Restarting bc monitoring")
		}

		// Extract timestamps from the matched log entries
		def restartTimestamps = restartLogs*.timeStamp
		// Ensure we have at least two entries to calculate intervals
		assert restartTimestamps.size() >= 2

		// Pair timestamps to calculate intervals between consecutive logs
		def intervals = restartTimestamps.collate(2, 1, false).collect {
			it[1] - it[0]
		}
		// Verify that each interval is approximately 3 seconds (±500ms tolerance)
		intervals.eachWithIndex { interval, i ->
			assert interval >= 2500 && interval <= 3500 : "Interval at index ${i} is ${interval}ms, expected ~3s"
		}

		and: "After restart then save success"
		assert messages.any {
			it.formattedMessage.contains("Success to register block number:") && it.level == Level.INFO
		}

		and: "After restart can process success"
		assert messages.formattedMessage.any{ it.contains("Success to process pending transactions")}
	}

	/**
	 * Should retry when save event of pending transaction error
	 * Expected: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)
	 * Service logs: Error in monitoring loop:
	 */
	def "Should retry when save event of pending transaction error" () {
		given: "The event pending transaction"
		// Mock event stream for avoid subscribe error
		setUpEventStream(Collections.emptyList())

		// Setup mock pending events
		def mockPendingEventLogs = createMockPendingEventLogs(["roleGranted"], 100L, "0xabc100")

		setUpPendingEvent(mockPendingEventLogs)


		// Mock the 'save' method to return false for the first two calls
		def saveCallCount = new AtomicInteger(0)
		doAnswer { invocation ->
			if (saveCallCount.incrementAndGet() <= 2) {
				return false // Mock 2 times call : false
			} else {
				return invocation.callRealMethod()
			}
		}.when(eventDao).save(any(Event))

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 15 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		def messages = logAppender.list


		and: "Service logs: Failed to save transaction"
		assert messages.any {
			it.formattedMessage.contains("Failed to save transaction") && it.level == Level.ERROR
		}

		and: "Service logs: Error in monitoring loop:"
		assert messages.any {
			it.formattedMessage.contains("Error in monitoring loop:") && it.level == Level.ERROR
		}

		and: "Service restart interval ~3s"
		assert messages.formattedMessage.any { it.contains("Restarting bc monitoring") }
		// Filter log entries that contain "Restarting bc monitoring"
		def restartLogs = logAppender.list.findAll {
			it.formattedMessage.contains("Restarting bc monitoring")
		}

		// Extract timestamps from the matched log entries
		def restartTimestamps = restartLogs*.timeStamp
		// Ensure we have at least two entries to calculate intervals
		assert restartTimestamps.size() >= 2

		// Pair timestamps to calculate intervals between consecutive logs
		def intervals = restartTimestamps.collate(2, 1, false).collect {
			it[1] - it[0]
		}
		// Verify that each interval is approximately 3 seconds (±500ms tolerance)
		intervals.eachWithIndex { interval, i ->
			assert interval >= 2500 && interval <= 3500 : "Interval at index ${i} is ${interval}ms, expected ~3s"
		}

		and: "After restart can process success"
		assert messages.formattedMessage.any{ it.contains("Success to process pending transactions")}
	}

	/**
	 * Should retry when new transaction with transaction hash is null
	 * Expected: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)
	 * Service logs: Error in monitoring loop:
	 */
	def "Should retry when new transaction with transaction hash is null" () {
		given: "The event has transaction hash is null"
		// Create a flowable that emits some notifications then errors to simulate disconnect
		// Starting from block 1000, creating 3 notifications (blocks 1000, 1001, 1002)
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		// Setup mock Web3j to return blocks with transactions and events with transaction hash is null
		def eventLogConfigs = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: null, blockNumber: 1002L]
		]
		setupMockWeb3jWithEvents(eventLogConfigs)
		// Setup mock pending events with empty transaction hash
		setUpPendingEvent(Collections.emptyList())

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 15 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 15, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		def messages = logAppender.list


		and: "Service logs: Event transaction hash is zero"
		assert messages.any {
			it.formattedMessage.contains("Event transaction hash is zero") && it.level == Level.ERROR
		}

		and: "Service logs: Error in monitoring loop:"
		assert messages.any {
			it.formattedMessage.contains("Error in monitoring loop:") && it.level == Level.ERROR
		}

		and: "Service log Restarting bc monitoring"
		assert messages.formattedMessage.any { it.contains("Restarting bc monitoring") }

		and: "After restart can process success"
		assert messages.formattedMessage.any{ it.contains("Success to process pending transactions")}
	}

	/**
	 * Should retry when new transaction with block height is zero
	 * Expected: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)
	 * Service logs: Error in monitoring loop:
	 */
	def "Should retry when new transaction with block height is zero" () {
		given: "The first event has block number zero, followed by an event with a valid block number"
		// Setup mock Web3j to return blocks with transactions and events
		def eventLogConfigs = [
			[logType: 'roleAdminChanged', txHash: '0x001', blockNumber: 0L],
			[logType: 'addTokenByProvider', txHash: '0x002', blockNumber: 1L]
		]
		// Starting from block 0, creating 5 notifications(0,1,2,3,4,5)
		def mockNotifications = createMockNewHeadsNotifications(0L, 5)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(eventLogConfigs)
		// Setup mock pending events with empty transaction hash
		setUpPendingEvent(Collections.emptyList())

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 20 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		def messages = logAppender.list


		and: "Service logs: Block height Number is zero"
		assert messages.any {
			it.formattedMessage.contains("Block height Number is zero") && it.level == Level.ERROR
		}

		and: "Service logs: Error in monitoring loop:"
		assert messages.any {
			it.formattedMessage.contains("Error in monitoring loop:") && it.level == Level.ERROR
		}

		and: "Service log Restarting bc monitoring"
		assert messages.formattedMessage.any { it.contains("Restarting bc monitoring") }

		and: "After restart can process success"
		assert messages.formattedMessage.any{ it.contains("Success to process new transactions")}
	}

	/**
	 * Should retry when save event of new transaction error
	 * Expected: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)
	 * Service logs: Error in monitoring loop:
	 */
	def "Should retry when save event of new transaction error" () {
		given: "The event new transaction"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 10)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)
		setUpPendingEvent(Collections.emptyList())

		// Mock the 'save' method to return false for the first two calls
		def saveCallCount = new AtomicInteger(0)
		doAnswer { invocation ->
			if (saveCallCount.incrementAndGet() <= 2) {
				return false // Mock 2 times call : false
			} else {
				return invocation.callRealMethod()
			}
		}.when(eventDao).save(any(Event))

		when: "BCMonitoring service starts and processes events"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		// Auto-stop service after 30 seconds to allow processing
		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 30, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "No exceptions are thrown during processing"
		noExceptionThrown()

		def messages = logAppender.list

		and: "Service logs: Failed to save transaction"
		assert messages.any {
			it.formattedMessage.contains("Failed to save transaction") && it.level == Level.ERROR
		}

		and: "Service logs: Error in monitoring loop:"
		assert messages.any {
			it.formattedMessage.contains("Error in monitoring loop:") && it.level == Level.ERROR
		}

		and: "Service log: Restarting bc monitoring"
		assert messages.formattedMessage.any { it.contains("Restarting bc monitoring") }

		and: "After restart can process success"
		assert messages.formattedMessage.any{ it.contains("Success to process new transactions")}
	}

	/**
	 * Should fails to start when required ABI download failure
	 * Verifies service fails
	 * Expected: Errors propagated to main function, service exits with error
	 * Service logs "Error starting bc monitoring"
	 */
	def "Should fails to start when required ABI download failure"() {
		given: "Invalid ABI file"

		AdhocHelper.uploadInvalidAbiFiles(s3Client, TEST_BUCKET, "3000", ["MalformedJson.json"])

		when: "Running service with command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 10, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "Service fails to start and not restart"
		def messages = logAppender.list*.formattedMessage
		assert !messages.any { it.contains("Started bc monitoring") }
		assert messages.any { it.contains("Error starting bc monitoring") }
		assert !messages.any { it.contains("Restarting bc monitoring") }
	}

	/**
	 * Should restart when handle invalid subscription check interval value invalid
	 * Service logs warning "restarting bc monitoring", continues infinite retry loop
	 * Service logs "Restarting bc monitoring"
	 */
	def "Should restart when handle invalid subscription check interval value invalid"() {
		given: "Invalid system property is set"
		properties.getSubscription().setCheckInterval("invalid")

		and: "Application is started via command line runner"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner)

		when: "Run command line with -f"
		scheduler.schedule({ AdhocHelper.stopBCMonitoring() }, 3, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then:
		assert logAppender.list.any { it.formattedMessage.contains("Restarting bc monitoring") && it.level == Level.ERROR }

		cleanup:
		properties.getSubscription().setCheckInterval("3000")
	}
}
