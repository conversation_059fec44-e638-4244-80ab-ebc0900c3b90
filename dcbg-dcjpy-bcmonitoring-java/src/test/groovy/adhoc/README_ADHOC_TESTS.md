# アドホックテストガイドライン

## はじめに

このドキュメントは、BCMonitoringプロジェクトにおけるアドホックテスト（Local IT）の作成・実行・保守に関する包括的なガイドラインである。

### 対象読者
- BCMonitoringプロジェクトの開発者
- テスト担当者
- 新規参画メンバー

### このガイドラインの使い方
1. **初回セットアップ**: セクション4「テスト実行」の環境設定を参照
2. **新規テスト作成**: セクション3「アドホックテストの作成」を参照
3. **既存テスト実行**: セクション4「テスト実行」を参照
4. **問題解決**: セクション5「トラブルシューティング」を参照

### 前提知識
- Groovy/Spockフレームワークの基本知識
- Spring Bootの基本概念
- Dockerの基本操作
- AWS（S3、DynamoDB）の基本概念

## 1. アドホックテストの概要

### 目的
アドホックテストは、BCMonitoringのLocal ITを実行するためのテストスイートだ。これらのテストは以下の目的で作成されている：

- **Local IT**: 実際のAWSサービス（S3、DynamoDB）とWeb3jを使用した完全なLocal IT
- **エンドツーエンドテスト**: アプリケーションの起動から終了までの完全なワークフローのテスト
- **回帰テスト**: 新機能追加や変更による既存機能への影響を検証

### 特徴
- **LocalStack**: AWS サービスのローカルエミュレーション
- **TestContainers**: Dockerコンテナを使用した分離されたテスト環境
- **Spock Framework**: Groovyベースの表現力豊かなテストフレームワーク
- **Spring Boot Test**: Spring BootアプリケーションのLocal IT機能

## 2. テスト構造ガイドライン

### ディレクトリ構造
```
src/test/groovy/adhoc/
├── base/                           # ベースクラス
│   └── BaseAdhocITSpec.groovy     # 共通のベースクラス
├── helper/                         # ヘルパークラス
│   └── AdhocHelper.groovy         # テスト用ユーティリティ
├── mock/                          # モッククラス
│   └── EventMockFactory.groovy    # イベントモック生成
├── abi/                           # ABI関連テスト
├── application_configuration/      # アプリケーション設定テスト
├── data_persistence/              # データ永続化テスト
├── error_handling/                # エラーハンドリングテスト
├── event_monitoring/              # イベントモニタリングテスト
├── logging_monitoring/            # ログ・モニタリングテスト
└── startup/                       # 起動関連テスト
```

### 命名規則

#### テストファイル命名
- **パターン**: `{機能名}ITSpec.groovy`
- **例**: `EventMonitoringITSpec.groovy`, `DataPersistenceITSpec.groovy`
- **ITSpec**: Integration Test Specificationの略

#### テストメソッド命名
- **パターン**: `"Should {期待される動作} when {条件}"`
- **例**: 
  ```groovy
  def "Should detects and processes events from new blockchain blocks"()
  def "Should handles websocket disconnect during active subscription"()
  def "Should rejects events with missing transaction hash"()
  ```

#### テストクラス命名
- **パターン**: `{機能名}ITSpec extends BaseAdhocITSpec`
- **例**: `EventMonitoringITSpec`, `DownloadAbiServiceITSpec`

### クラス階層とアノテーション

#### 基本構造
```groovy
@SpringBootTest(
    classes = [BcmonitoringApplication.class],
    webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class YourTestITSpec extends BaseAdhocITSpec {
    
    @Autowired
    ApplicationContext applicationContext
    
    @MockitoSpyBean
    Web3jConfig web3jConfig
    
    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }
}
```

#### 必須アノテーション
- `@SpringBootTest`: Spring Bootアプリケーションコンテキストの起動
- `@ActiveProfiles("test")`: テストプロファイルの有効化
- `@MockitoSpyBean`: Web3jのMockitoスパイビーンの注入

## 3. アドホックテストの作成

### ステップバイステップガイド

#### Step 1: テストクラスの作成
```groovy
package adhoc.your_feature

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
// 必要なインポート

@SpringBootTest(
    classes = [BcmonitoringApplication.class],
    webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class YourFeatureITSpec extends BaseAdhocITSpec {
    
    @Autowired
    ApplicationContext applicationContext
    
    @MockitoSpyBean
    Web3jConfig web3jConfig
    
    @Override
    Web3jConfig getWeb3jConfig() {
        return web3jConfig
    }
}
```

#### Step 2: セットアップとクリーンアップ
```groovy
def setupSpec() {
    setupSpecCommon()
}

def cleanupSpec() {
    cleanupSpecCommon()
}

def setup() {
    setupCommon()
    // テストケースの内容に応じて必要なABI.jsonファイルをS3へアップロードする
    AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
        "AccessCtrl", "Token", "Account"
    ])
}

def cleanup() {
    cleanupCommon()
}
```

#### Step 3: テストメソッドの実装
```groovy
def "Should perform expected behavior when specific condition"() {
    given: "初期条件の設定"
    // テストデータの準備
    def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
    setUpEventStream(mockNotifications)
    
    when: "アクションの実行"
    def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)
    // 永遠実行を防ぐために自動停止を設定する
    scheduler.schedule({
        AdhocHelper.stopBCMonitoring()
    }, 15, TimeUnit.SECONDS)
    commandLineRunner.run("-f")
    
    then: "結果の検証"
    noExceptionThrown()
    
    and: "追加の検証"
    def messages = logAppender.list*.formattedMessage
    assert messages.any { it.contains("Expected log message") }
}
```

### 共通パターンとユーティリティ

#### モックイベントストリームの設定
```groovy
// 新しいBlock通知のモック作成
def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
setUpEventStream(mockNotifications)

// 通知された新しいBlockに対してイベントログのモック設定
def eventLogConfigs = [
    [logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
    [logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L]
]
setupMockWeb3jWithEvents(eventLogConfigs)

// ペンディングイベントのモック設定
def mockPendingEventLogs = createMockPendingEventLogs([
    "addProviderRole", "addTokenByProvider"
], 200L, "0xabc")
setUpPendingEvent(mockPendingEventLogs)
```

#### ログ検証パターン
```groovy
def messages = logAppender.list*.formattedMessage
assert messages.any { it.contains("Started bc monitoring") }
assert messages.any { it.contains("Event found tx_hash=") }
assert messages.any { it.contains("Success to register event") }
```

#### DynamoDB検証パターン
```groovy
def eventsInDb = AdhocHelper.scanEventsTable(dynamoDbClient, EVENTS_TABLE)
def specificEvent = eventsInDb.find { event ->
    event.get("name").s() == "AddProviderRole" &&
    event.get("transactionHash").s() == "0xabc123"
}
assert specificEvent != null
```

## 4. テスト実行

### テスト実行環境
#### Docker推奨設定
- Docker Desktop または Docker Engine
- Docker Compose
- 最小メモリ: 4GB
- 最小ディスク容量: 10GB 

＊事前にDockerを起動しておくこと

### 個別テスト実行
> **⚠️ 注意**
> `testAdhoc`のgradleタスクは独自実装のものである。
> `build`タスクなどに含まれている`test`タスクからは`src/test/groovy/adhoc`配下のテストクラス実行は除外しているので注意すること。

#### Gradle経由
```bash
# 特定のテストクラス実行
./gradlew testAdhoc --tests "adhoc.event_monitoring.EventMonitoringITSpec"

# 特定のテストメソッド実行
./gradlew testAdhoc --tests "adhoc.event_monitoring.EventMonitoringITSpec.Should detects and processes events from new blockchain blocks"
```

#### IDE実行
- IntelliJ IDEA: テストクラスまたはメソッドを右クリック → "Run"

### テストスイート実行

#### 全アドホックテスト実行
```bash
./gradlew testAdhoc
```

#### カテゴリ別実行
```bash
# イベントモニタリング関連テスト
./gradlew testAdhoc --tests "adhoc.event_monitoring.*"

# エラーハンドリング関連テスト
./gradlew testAdhoc --tests "adhoc.error_handling.*"

# データ永続化関連テスト
./gradlew testAdhoc --tests "adhoc.data_persistence.*"
```

## 5. トラブルシューティング
> **⚠️ 注意**
> ローカルでテスト実施中、今までエラーが出ていないにも関わらず、突然コンテナの立ち上げができないエラーが発生する場合がある。 
> その場合、以下2つの原因の可能性が高い。

### 1 Dockerのイメージがいっぱいになった場合
対処法：dockerのイメージを削除

### 2 IPアドレスを使い切った場合
対処法：docker network lsで確認した上で、docker network pruneでIPアドレスを削除

---

本ガイドラインに従って、一貫性のある高品質なアドホックテストを作成する。