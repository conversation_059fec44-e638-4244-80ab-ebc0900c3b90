package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * A custom implementation of the {@link ParameterizedType} interface.
 *
 * <p>This class is used to represent parameterized types (e.g., {@code List<String>}) at runtime,
 * which is useful when working with Java's generic type system via reflection. It allows specifying
 * both the raw type (e.g., {@code List.class}) and the actual type arguments (e.g., {@code
 * String.class}).
 *
 * <p><strong>Note:</strong> This implementation always returns {@code null} for {@link
 * #getOwnerType()}, assuming that the parameterized type is not a member of another type.
 *
 * <p>Example usage:
 *
 * <pre>{@code
 * ParameterizedType type = new ParameterizedTypeImpl(List.class, String.class);
 * }</pre>
 */
public class ParameterizedTypeImpl implements ParameterizedType {

  private final Type rawType;
  private final Type[] typeArguments;

  /**
   * Constructs a {@code ParameterizedTypeImpl} with the given raw type and type arguments.
   *
   * @param rawType the raw class type (e.g., {@code List.class})
   * @param typeArguments the actual type arguments (e.g., {@code String.class})
   */
  public ParameterizedTypeImpl(Type rawType, Type... typeArguments) {
    this.rawType = rawType;
    this.typeArguments = typeArguments;
  }

  /**
   * Returns the actual type arguments for this parameterized type.
   *
   * @return an array of {@link Type} representing the actual type arguments
   */
  @Override
  public Type[] getActualTypeArguments() {
    return typeArguments;
  }

  /**
   * Returns the raw type of this parameterized type.
   *
   * @return the raw {@link Type} (e.g., {@code List.class})
   */
  @Override
  public Type getRawType() {
    return rawType;
  }

  /**
   * Returns the owner type of this type, or {@code null} if it is a top-level type.
   *
   * @return always {@code null} in this implementation
   */
  @Override
  public Type getOwnerType() {
    return null;
  }
}
