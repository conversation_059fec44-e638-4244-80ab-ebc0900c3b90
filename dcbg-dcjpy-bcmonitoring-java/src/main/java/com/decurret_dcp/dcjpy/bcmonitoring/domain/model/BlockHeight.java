package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;

import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

@Builder
public class BlockHeight {
  public final long id;
  public final long blockNumber;

  /**
   * Build q BlockHeight from a map of attribute values.
   *
   * @param attributeValueMap A map containing the block height data.
   * @return a BlockHeight object.
   */
  public static BlockHeight of(Map<String, AttributeValue> attributeValueMap) {
    return BlockHeight.builder()
        .id(Long.valueOf(attributeValueMap.get("id").n()))
        .blockNumber(Long.valueOf(attributeValueMap.get("blockNumber").n()))
        .build();
  }

  /**
   * Converts the BlockHeight object to a Map<String, AttributeValue>.
   *
   * @return A map containing the block height data.
   */
  public Map<String, AttributeValue> toAttributeMap() {
    Map<String, AttributeValue> item = new HashMap<>();
    item.put("id", AttributeValue.builder().n(String.valueOf(1)).build());
    item.put("blockNumber", AttributeValue.builder().n(String.valueOf(blockNumber)).build());
    return item;
  }
}
