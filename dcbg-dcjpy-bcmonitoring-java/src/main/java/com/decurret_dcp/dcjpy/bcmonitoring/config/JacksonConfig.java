package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import java.io.IOException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JacksonConfig {

  @Bean
  public ObjectMapper blockchainObjectMapper() {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

    SimpleModule module = new SimpleModule();
    module.addSerializer(
        byte[].class,
        new JsonSerializer<byte[]>() {
          @Override
          public void serialize(byte[] value, JsonGenerator gen, SerializerProvider serializers)
              throws IOException {
            gen.writeStartArray();
            for (byte b : value) {
              gen.writeNumber(b);
            }
            gen.writeEndArray();
          }
        });
    objectMapper.registerModule(module);

    return objectMapper;
  }
}
