package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService;
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import java.util.Objects;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MonitoringRunnerConfig {

  private final LoggingService logger;
  private final MonitorEventService monitorEventService;
  private final DownloadAbiService downloadAbiService;
  private final BcmonitoringConfigurationProperties properties;

  public MonitoringRunnerConfig(
      LoggingService logger,
      MonitorEventService monitorEventService,
      DownloadAbiService downloadAbiService,
      BcmonitoringConfigurationProperties properties) {
    this.logger = logger;
    this.monitorEventService = monitorEventService;
    this.downloadAbiService = downloadAbiService;
    this.properties = properties;
  }

  /**
   * This method checks if monitoring should start immediately: - If the eagerStart flag is set in
   * the properties + eagerStart is typically a configuration property used to control whether a
   * service or component should start automatically when the application boots up. - If the command
   * line argument "-f" is provided + The -f is a command-line argument used as a manual override to
   * start the monitoring process when the application runs. It's an alternative to setting
   * eagerStart=true in your configuration.
   */
  @Bean
  public CommandLineRunner commandLineRunner() {
    return args -> {
      if (properties.isEagerStart()
          || (args != null && args.length > 0 && Objects.equals(args[0], "-f"))) {
        startBCMonitoring();
      }
    };
  }

  /**
   * This method is used to start the monitoring process. It will run in a virtual thread and will
   * monitor events. If an error occurs, it will restart the monitoring process after a delay.
   */
  private void startBCMonitoring() {
    logger.info("Starting bc monitoring");
    try {
      downloadAbiService.execute();
      logger.info("Started bc monitoring");
      // Start monitoring events
      while (ContextConfig.isServiceRunning()) {
        try {
          logger.info("Monitoring events...");
          monitorEventService.execute();
        } catch (Exception e) {
          logger.error("Restarting bc monitoring");
        }
      }
    } catch (Exception e) {
      logger.error("Error starting bc monitoring", e);
    }
  }
}
