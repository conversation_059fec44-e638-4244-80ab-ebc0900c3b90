package com.decurret_dcp.dcjpy.bcmonitoring.exception;

/** Exception thrown when cannot detect Type of abi from Solidity type. */
public class UnsupportedTypeException extends BcmonitoringException {

  private static final String ERROR_CODE = "UNSUPPORTED_TYPE_ERROR";

  /**
   * Constructs a new UnsupportedTypeException with the specified detail message.
   *
   * @param message the detail message
   */
  public UnsupportedTypeException(String message) {
    super(ERROR_CODE, message);
  }

  /**
   * Constructs a new UnsupportedTypeException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of this exception
   */
  public UnsupportedTypeException(String message, Throwable cause) {
    super(ERROR_CODE, message, cause);
  }
}
