package com.decurret_dcp.dcjpy.bcmonitoring.consts;

public class DCFConst {

  // Format date time
  public static final String DATETIME_FORMAT_API = "yyyy-MM-dd'T'HH:mm:ss";
  public static final String TIMEZONE_API = "Asia/Tokyo";
  public static final String TIMEZONE_DB = "GMT";

  // Environment
  public static final String PROD = "prod";
  public static final String LOCAL = "local";
  public static final String TEST = "test";

  // Specific characters
  public static final String SLASH = "/";
  public static final String DOT = ".";
  public static final String EMPTY = "";

  // Constants
  public static final String ADDRESS = "address";
  public static final String STRING = "string";
  public static final String BOOL = "bool";
  public static final String BOOLEAN = "boolean";
  public static final String BYTE = "byte";
  public static final String BYTES = "bytes";
  public static final String DOUBLE = "double";
  public static final String FLOAT = "float";
  public static final String CHAR = "char";
  public static final String SHORT = "short";
  public static final String LONG = "long";

  // Integers
  public static final String INT = "int";
  public static final String INT_8 = "int8";
  public static final String INT_16 = "int16";
  public static final String INT_24 = "int24";
  public static final String INT_32 = "int32";
  public static final String INT_40 = "int40";
  public static final String INT_48 = "int48";
  public static final String INT_56 = "int56";
  public static final String INT_64 = "int64";
  public static final String INT_72 = "int72";
  public static final String INT_80 = "int80";
  public static final String INT_88 = "int88";
  public static final String INT_96 = "int96";
  public static final String INT_104 = "int104";
  public static final String INT_112 = "int112";
  public static final String INT_120 = "int120";
  public static final String INT_128 = "int128";
  public static final String INT_136 = "int136";
  public static final String INT_144 = "int144";
  public static final String INT_152 = "int152";
  public static final String INT_160 = "int160";
  public static final String INT_168 = "int168";
  public static final String INT_176 = "int176";
  public static final String INT_184 = "int184";
  public static final String INT_192 = "int192";
  public static final String INT_200 = "int200";
  public static final String INT_208 = "int208";
  public static final String INT_216 = "int216";
  public static final String INT_224 = "int224";
  public static final String INT_232 = "int232";
  public static final String INT_240 = "int240";
  public static final String INT_248 = "int248";
  public static final String INT_256 = "int256";

  // Unsigned integers
  public static final String UINT = "uint";
  public static final String UINT_8 = "uint8";
  public static final String UINT_16 = "uint16";
  public static final String UINT_24 = "uint24";
  public static final String UINT_32 = "uint32";
  public static final String UINT_40 = "uint40";
  public static final String UINT_48 = "uint48";
  public static final String UINT_56 = "uint56";
  public static final String UINT_64 = "uint64";
  public static final String UINT_72 = "uint72";
  public static final String UINT_80 = "uint80";
  public static final String UINT_88 = "uint88";
  public static final String UINT_96 = "uint96";
  public static final String UINT_104 = "uint104";
  public static final String UINT_112 = "uint112";
  public static final String UINT_120 = "uint120";
  public static final String UINT_128 = "uint128";
  public static final String UINT_136 = "uint136";
  public static final String UINT_144 = "uint144";
  public static final String UINT_152 = "uint152";
  public static final String UINT_160 = "uint160";
  public static final String UINT_168 = "uint168";
  public static final String UINT_176 = "uint176";
  public static final String UINT_184 = "uint184";
  public static final String UINT_192 = "uint192";
  public static final String UINT_200 = "uint200";
  public static final String UINT_208 = "uint208";
  public static final String UINT_216 = "uint216";
  public static final String UINT_224 = "uint224";
  public static final String UINT_232 = "uint232";
  public static final String UINT_240 = "uint240";
  public static final String UINT_248 = "uint248";
  public static final String UINT_256 = "uint256";

  // Bytes
  public static final String BYTES_1 = "bytes1";
  public static final String BYTES_2 = "bytes2";
  public static final String BYTES_3 = "bytes3";
  public static final String BYTES_4 = "bytes4";
  public static final String BYTES_5 = "bytes5";
  public static final String BYTES_6 = "bytes6";
  public static final String BYTES_7 = "bytes7";
  public static final String BYTES_8 = "bytes8";
  public static final String BYTES_9 = "bytes9";
  public static final String BYTES_10 = "bytes10";
  public static final String BYTES_11 = "bytes11";
  public static final String BYTES_12 = "bytes12";
  public static final String BYTES_13 = "bytes13";
  public static final String BYTES_14 = "bytes14";
  public static final String BYTES_15 = "bytes15";
  public static final String BYTES_16 = "bytes16";
  public static final String BYTES_17 = "bytes17";
  public static final String BYTES_18 = "bytes18";
  public static final String BYTES_19 = "bytes19";
  public static final String BYTES_20 = "bytes20";
  public static final String BYTES_21 = "bytes21";
  public static final String BYTES_22 = "bytes22";
  public static final String BYTES_23 = "bytes23";
  public static final String BYTES_24 = "bytes24";
  public static final String BYTES_25 = "bytes25";
  public static final String BYTES_26 = "bytes26";
  public static final String BYTES_27 = "bytes27";
  public static final String BYTES_28 = "bytes28";
  public static final String BYTES_29 = "bytes29";
  public static final String BYTES_30 = "bytes30";
  public static final String BYTES_31 = "bytes31";
  public static final String BYTES_32 = "bytes32";
}
