package com.decurret_dcp.dcjpy.bcmonitoring.config;

import java.util.concurrent.atomic.AtomicBoolean;

public class ContextConfig {
  private static final AtomicBoolean serviceRunning = new AtomicBoolean(true);

  public static boolean isServiceRunning() {
    return serviceRunning.get();
  }

  public static void setServiceRunning(boolean serviceRunning) {
    ContextConfig.serviceRunning.set(serviceRunning);
  }
}
