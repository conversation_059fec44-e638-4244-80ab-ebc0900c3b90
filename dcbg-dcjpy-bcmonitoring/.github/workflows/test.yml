name: Unit Test

on:
  - pull_request

jobs:
  run_unit_test:
    name: Run unit test
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v3
      - name: Set up go
        uses: actions/setup-go@v3
        with:
          go-version: '1.19.2'
      - name: <PERSON> execute permission for script
        run: chmod +x ./scripts/run-test.sh
      - run: ./scripts/run-test.sh
