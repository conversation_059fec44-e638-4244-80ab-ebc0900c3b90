// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/adapter"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/dynamodb"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/s3"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase"
	"github.com/sirupsen/logrus"
)

// Injectors from wire.go:

func initializeMonitorInteractor(entry *logrus.Entry) (*usecase.MonitorEventInteractor, error) {
	context := _wireContextValue
	ethEventLogDao, err := ethereum.NewEventLogDao(context, entry)
	if err != nil {
		return nil, err
	}
	eventLogRepository := adapter.NewEventLogRepository(entry, ethEventLogDao)
	dynamoEventDao := dynamodb.NewDynamoEventDao(context, entry)
	eventRepository := adapter.NewEventRepository(entry, dynamoEventDao)
	dynamoBlockHeightDao := dynamodb.NewDynamoBlockHeightDao(entry, context)
	blockHeightRepository := adapter.NewBlockHeightRepository(entry, dynamoBlockHeightDao)
	monitorEventInteractor := usecase.NewMonitorEventInteractor(context, entry, eventLogRepository, eventRepository, blockHeightRepository)
	return monitorEventInteractor, nil
}

var (
	_wireContextValue = context.Background()
)

func initializeDownloadAbiInteractor(entry *logrus.Entry) *usecase.DownloadAbiInteractor {
	s3AbiDao := s3.NewS3AbiDao(entry)
	s3AbiDaoRepository := adapter.NewS3AbiDaoRepository(entry, s3AbiDao)
	downloadAbiInteractor := usecase.NewDownloadAbiInteractor(entry, s3AbiDaoRepository)
	return downloadAbiInteractor
}
