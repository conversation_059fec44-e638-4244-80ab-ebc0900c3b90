//go:build wireinject
// +build wireinject

package main

import (
	"context"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/adapter"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/dynamodb"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/s3"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase"
	"github.com/google/wire"
	"github.com/sirupsen/logrus"
)

func initializeMonitorInteractor(*logrus.Entry) (*usecase.MonitorEventInteractor, error) {
	wire.Build(
		wire.InterfaceValue(new(context.Context), context.Background()),

		usecase.NewMonitorEventInteractor,

		adapter.NewEventLogRepository,
		wire.Bind(new(usecase.EventLogRepository), new(*adapter.EventLogRepository)),

		adapter.NewEventRepository,
		wire.Bind(new(usecase.EventRepository), new(*adapter.EventRepository)),

		adapter.NewBlockHeightRepository,
		wire.Bind(new(usecase.BlockHeightRepository), new(*adapter.BlockHeightRepository)),

		ethereum.NewEventLogDao,
		wire.Bind(new(adapter.EventLogDao), new(*ethereum.EthEventLogDao)),

		dynamodb.NewDynamoEventDao,
		wire.Bind(new(adapter.EventDao), new(*dynamodb.DynamoEventDao)),

		dynamodb.NewDynamoBlockHeightDao,
		wire.Bind(new(adapter.BlockHeightDao), new(*dynamodb.DynamoBlockHeightDao)),
	)

	return nil, nil
}

func initializeDownloadAbiInteractor(*logrus.Entry) *usecase.DownloadAbiInteractor {
	wire.Build(
		usecase.NewDownloadAbiInteractor,

		adapter.NewS3AbiDaoRepository,
		wire.Bind(new(usecase.S3AbiDaoRepository), new(*adapter.S3AbiDaoRepository)),

		s3.NewS3AbiDao,
		wire.Bind(new(adapter.S3AbiDao), new(*s3.S3AbiDao)),
	)

	return nil
}
