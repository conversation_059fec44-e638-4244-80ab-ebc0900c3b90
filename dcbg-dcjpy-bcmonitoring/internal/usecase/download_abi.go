package usecase

import (
	"fmt"
	"io"
	"path/filepath"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/config"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
)

const FILEPATH_DELIMITER = "/"

type DownloadAbiInteractor struct {
	l                  *logrus.Entry
	S3AbiDaoRepository S3AbiDaoRepository
}

func NewDownloadAbiInteractor(l *logrus.Entry, s3AbiDaoRepository S3AbiDaoRepository) *DownloadAbiInteractor {
	return &DownloadAbiInteractor{
		l:                  l,
		S3AbiDaoRepository: s3AbiDaoRepository,
	}
}

func (m *DownloadAbiInteractor) Execute() error {
	bucketName := config.GetS3BucketName()
	m.l.WithFields(logrus.Fields{"bucket_name": bucketName}).Infoln("downloading abi files...")

	listCommonPrefixes, err := m.S3AbiDaoRepository.ListCommonPrefixesObjects(bucketName)
	if err != nil {
		return fmt.Errorf("failed to list s3 CommonPrefixes objects %v\n", err)
	}

	for _, commonPrefix := range listCommonPrefixes.CommonPrefixes {
		prefix := aws.ToString(commonPrefix.Prefix)
		listOutput, err := m.S3AbiDaoRepository.ListObjects(bucketName, prefix[:len(prefix)-1])
		if err != nil {
			return fmt.Errorf("failed to list s3 abi objects %v\n", err)
		}

		for _, obj := range listOutput.Contents {
			objKey := aws.ToString(obj.Key)
			// 直下のみが処理対象
			if strings.Count(objKey, "/") > 1 {
				continue
			}
			if filepath.Ext(objKey) != ".json" {
				fmt.Printf("This object will be skipped because the extension is not .json.: %s.\n", objKey)
				continue
			}
			m.l.WithFields(logrus.Fields{
				"bucketName": bucketName,
				"objKey":     objKey,
			}).Infof("getting s3 abi object.")
			getOutput, err := m.S3AbiDaoRepository.GetObject(bucketName, objKey)
			if err != nil {
				return fmt.Errorf("failed to get s3 abi object: %s. %v\n", objKey, err)
			}
			err = m.parseAbiContent(getOutput.Body, obj)
			if err != nil {
				return fmt.Errorf("failed to parse s3 abi object: %s. %v\n", objKey, err)
			}
		}
	}

	return nil
}

func (m *DownloadAbiInteractor) parseAbiContent(body io.ReadCloser, obj types.Object) error {
	defer body.Close()

	abiJson, err := io.ReadAll(body)
	if err != nil {
		return err
	}

	objKey := aws.ToString(obj.Key)
	contractName := strings.Replace(strings.Split(objKey, "/")[1], ".json", "", -1) // contract nameはパスとファイル名を加工して取得する
	lastModified := obj.LastModified
	abiFormat := config.GetABIFormat()
	var address string
	// TruffleとHardhatではcontract addressの記載箇所が異なるため、起動時に設定した環境変数から処理を分岐させる
	if abiFormat == "truffle" {
		address = strings.ToLower(gjson.GetBytes(abiJson, "networks.*.address").String())
	} else {
		address = strings.ToLower(gjson.GetBytes(abiJson, "address").String())
	}

	abiObject := new(abi.ABI)
	abiNode := gjson.GetBytes(abiJson, "abi")
	err = abiObject.UnmarshalJSON([]byte(abiNode.Raw))
	if err != nil {
		m.l.Errorf("failed to unmarshal abi json: %s. %v\n", contractName, err)
		return err
	}

	// ethereumモジュールに保持する
	ethereum.AppendContractAddresses(address)
	ethereum.AppendContractEventStore(address, contractName, abiObject.Events)

	m.l.WithFields(logrus.Fields{
		"address":       address,
		"contract_name": contractName,
		"last_modified": lastModified,
		"events":        abiObject.Events,
	}).Infof("abi files downloaded.")

	return nil
}
