package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/logger"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase/mock"
	"go.uber.org/mock/gomock"
)

func TestMonitorEventInteractor_Execute(t *testing.T) {
	l := logger.NewLogger("test")
	t.Run("未取得eventの保存が正常に行われること", func(t *testing.T) {
		err := os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		if err != nil {
			t.<PERSON>rrorf("failed to set env")
		}
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{},
				entity.BlockHeight{},
			},
		}
		go func() {
			defer close(transactions)
			for _, tx := range transactionList {
				pendingTransactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventPos := 0
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Times(len(transactionList) - 1).DoAndReturn(
			func(evt entity.Event) bool {
				if transactionList[eventPos].Events[0].Name != evt.Name {
					t.Errorf("expect: %s, actual: %s\n", transactionList[eventPos].Events[0].Name, evt.Name)
				}
				eventPos += 1
				return true
			})
		blockPos := 0
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(len(transactionList) - 1).DoAndReturn(
			func(block entity.BlockHeight) bool {
				if transactionList[blockPos].BlockHeight.BlockNumber != block.BlockNumber {
					t.Errorf("expect: %v, actual: %v\n", transactionList[blockPos].BlockHeight.BlockNumber, block.BlockNumber)
				}
				blockPos += 1
				return true
			})

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("新規eventの保存が正常に行われること", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{},
				entity.BlockHeight{},
			},
		}
		go func() {
			close(pendingTransactions)
			defer close(transactions)
			for _, tx := range transactionList {
				transactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventPos := 0
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Times(len(transactionList) - 1).DoAndReturn(
			func(evt entity.Event) bool {
				if transactionList[eventPos].Events[0].Name != evt.Name {
					t.Errorf("expect: %s, actual: %s\n", transactionList[eventPos].Events[0].Name, evt.Name)
				}
				eventPos += 1
				return true
			})
		blockPos := 0
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(len(transactionList) - 1).DoAndReturn(
			func(block entity.BlockHeight) bool {
				if transactionList[blockPos].BlockHeight.BlockNumber != block.BlockNumber {
					t.Errorf("expect: %v, actual: %v\n", transactionList[blockPos].BlockHeight.BlockNumber, block.BlockNumber)
				}
				blockPos += 1
				return true
			})

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("未取得eventの取得に失敗した場合（transaction hash）", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: ""}},
				entity.BlockHeight{BlockNumber: 1},
			},
		}
		go func() {
			defer close(transactions)
			for _, tx := range transactionList {
				pendingTransactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("未取得eventの取得に失敗した場合（block number）", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}},
				entity.BlockHeight{BlockNumber: 0},
			},
		}
		go func() {
			defer close(transactions)
			for _, tx := range transactionList {
				pendingTransactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("新規eventの取得に失敗した場合（transaction hash）", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: ""}},
				entity.BlockHeight{BlockNumber: 1},
			},
		}
		go func() {
			close(pendingTransactions)
			defer close(transactions)
			for _, tx := range transactionList {
				transactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("新規eventの取得に失敗した場合（block number）", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}},
				entity.BlockHeight{BlockNumber: 0},
			},
		}
		go func() {
			close(pendingTransactions)
			defer close(transactions)
			for _, tx := range transactionList {
				transactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("eventの保存に失敗した場合", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{},
				entity.BlockHeight{},
			},
		}
		go func() {
			defer close(transactions)
			for _, tx := range transactionList {
				pendingTransactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Return(false)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(0)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("blockHeightの保存に失敗した場合", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{},
				entity.BlockHeight{},
			},
		}
		go func() {
			defer close(transactions)
			for _, tx := range transactionList {
				pendingTransactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Return(true).Times(1)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Return(false)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("block内に複数個イベントが存在する場合、blockHeightの保存は1回しか呼ばれないこと", func(t *testing.T) {
		os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}, {TransactionHash: "0x0001"}, {TransactionHash: "0x0002"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{},
				entity.BlockHeight{},
			},
		}
		go func() {
			defer close(transactions)
			for _, tx := range transactionList {
				pendingTransactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Return(true).Times(3)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Return(true).Times(1)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})

	t.Run("pendingされたtxのblockHeightの更新は、同blockHeightのイベントのputが全て完了された後に行うこと", func(t *testing.T) {
		err := os.Setenv("SUBSCRIPTION_CHECK_INTERVAL", "1")
		if err != nil {
			t.Errorf("failed to set env")
		}
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()
		cctx, _ := context.WithCancel(ctx)

		// ログ取得
		transactions := make(chan entity.Transaction)
		pendingTransactions := make(chan entity.Transaction)
		transactionList := []entity.Transaction{
			{
				entity.Events{{TransactionHash: "0x0000"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{{TransactionHash: "0x0001"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{{TransactionHash: "0x0002"}},
				entity.BlockHeight{BlockNumber: 1},
			},
			{
				entity.Events{},
				entity.BlockHeight{},
			},
		}
		go func() {
			defer close(transactions)
			for _, tx := range transactionList {
				pendingTransactions <- tx
			}
		}()
		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventLogRepositoryMock.EXPECT().Subscribe(cctx).Return(transactions, nil)
		var u uint64 = 2
		eventLogRepositoryMock.EXPECT().GetFilterLogs(cctx, u).Return(pendingTransactions, nil)

		// イベントとして保存
		eventPos := 0
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		eventRepositoryMock.EXPECT().Save(gomock.Any()).Times(len(transactionList) - 1).DoAndReturn(
			func(evt entity.Event) bool {
				if transactionList[eventPos].Events[0].Name != evt.Name {
					t.Errorf("expect: %s, actual: %s\n", transactionList[eventPos].Events[0].Name, evt.Name)
				}
				eventPos += 1
				return true
			})
		blockPos := 0
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)
		blockHeightRepositoryMock.EXPECT().Get().Do(func() {
			blockHeightRepositoryMock.EXPECT().Get().Return(entity.BlockHeight{}, fmt.Errorf("error mock"))
		}).Return(entity.BlockHeight{BlockNumber: 1}, nil)

		blockHeightRepositoryMock.EXPECT().Save(gomock.Any()).Times(1).DoAndReturn(
			func(block entity.BlockHeight) bool {
				if transactionList[blockPos].BlockHeight.BlockNumber != block.BlockNumber {
					t.Errorf("expect: %v, actual: %v\n", transactionList[blockPos].BlockHeight.BlockNumber, block.BlockNumber)
				}
				blockPos += 1
				return true
			})

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		interactor.Execute()
	})
}

func TestEthEventLogDao_fetchTraceId(t *testing.T) {
	l := logger.NewLogger("test")
	t.Run("JSON文字列に含まれるtraceIdを取得できること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()

		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		value := map[string][]byte{"traceId": {51, 53, 51, 100, 97, 99, 102, 54, 55, 50, 54, 57, 51, 54, 51, 102, 99, 55, 53, 98, 99, 100, 102, 57, 99, 56, 102, 57, 48, 53, 50, 99}}
		param, err := json.Marshal(value)
		if err != nil {
			t.Errorf("json parse error %v\n", err)
		}
		traceId := interactor.fetchTraceId(string(param))
		expect := "353dacf67269363fc75bcdf9c8f9052c"
		if traceId != expect {
			t.Errorf("expect: %v, actual: %v\n", expect, traceId)
		}
	})

	t.Run("0から16までのUTF-8文字列に変換できること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()

		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		value := map[string][]byte{"traceId": {48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 97, 98, 99, 100, 101, 102}}
		param, err := json.Marshal(value)
		if err != nil {
			t.Errorf("json parse error %v\n", err)
		}
		traceId := interactor.fetchTraceId(string(param))
		expect := "0123456789abcdef"
		if traceId != expect {
			t.Errorf("expect: %v, actual: %v\n", expect, traceId)
		}
	})

	t.Run("JSON文字列にtraceIdが含まれない場合、空文字が返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()

		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		traceId := interactor.fetchTraceId("{}")
		if traceId != "" {
			t.Errorf("expect: %v, actual: %v\n", "", traceId)
		}
	})

	t.Run("パースエラー時に空文字が返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		ctx := context.Background()

		eventLogRepositoryMock := mock.NewMockEventLogRepository(ctrl)
		eventRepositoryMock := mock.NewMockEventRepository(ctrl)
		blockHeightRepositoryMock := mock.NewMockBlockHeightRepository(ctrl)

		// テスト実行
		interactor := NewMonitorEventInteractor(ctx, l, eventLogRepositoryMock, eventRepositoryMock, blockHeightRepositoryMock)
		traceId := interactor.fetchTraceId("")
		if traceId != "" {
			t.Errorf("expect: %v, actual: %v\n", "", traceId)
		}
	})
}
