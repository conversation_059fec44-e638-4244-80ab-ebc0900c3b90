package usecase

import (
	"errors"
	"testing"

	"github.com/sirupsen/logrus"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/usecase/mock"
	"go.uber.org/mock/gomock"
)

func TestDownloadAbiInteractor_Execute(t *testing.T) {
	l := logrus.NewEntry(logrus.New())
	l.Logger.SetLevel(logrus.ErrorLevel)

	t.Run("複数ゾーン分のS3ディレクトリ一覧とオブジェクト一覧とオブジェクト取得が実行されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		defer func() { recover() }()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)

		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(&s3.ListObjectsV2Output{
			CommonPrefixes: []types.CommonPrefix{
				{Prefix: aws.String("3000")},
				{Prefix: aws.String("3001")},
				{Prefix: aws.String("3002")}}},
			nil)
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(&s3.ListObjectsV2Output{
			Contents: []types.Object{
				{Key: aws.String("3000/test1.json")},
				{Key: aws.String("3001/test2.json")},
				{Key: aws.String("3002/test3.json")}}},
			nil).AnyTimes()
		// オブジェクト取得が呼ばれる
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&s3.GetObjectOutput{}, nil).Times(1)
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

	t.Run("単一ゾーンのS3ディレクトリ一覧とオブジェクト一覧とオブジェクト取得が実行されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		defer func() { recover() }()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)
		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(&s3.ListObjectsV2Output{
			CommonPrefixes: []types.CommonPrefix{
				{Prefix: aws.String("3000")}}},
			nil)
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(&s3.ListObjectsV2Output{
			Contents: []types.Object{
				{Key: aws.String("3000/test1.json.test")},
			}},
			nil).AnyTimes()
		// オブジェクト取得が呼ばれる
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&s3.GetObjectOutput{}, nil).AnyTimes()
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

	t.Run("S3のオブジェクト一覧は表示されるがオブジェクト取得が実行されないこと", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		defer func() { recover() }()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)
		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(&s3.ListObjectsV2Output{
			CommonPrefixes: []types.CommonPrefix{
				{Prefix: aws.String("3000")},
				{Prefix: aws.String("3001")},
				{Prefix: aws.String("3002")}}},
			nil)
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(&s3.ListObjectsV2Output{
			Contents: []types.Object{
				{Key: aws.String("3000/test1.json.test")},
				{Key: aws.String("3001/test2.json.test")},
				{Key: aws.String("3002/test3.json.test")},
			}},
			nil).AnyTimes()
		//// オブジェクト取得が呼ばれる
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(nil, nil).Times(0)
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

	t.Run("S3のオブジェクト一覧が0件の場合、オブジェクト取得が実行されないこと", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)
		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(&s3.ListObjectsV2Output{
			CommonPrefixes: []types.CommonPrefix{
				{Prefix: aws.String("3000")},
				{Prefix: aws.String("3001")},
				{Prefix: aws.String("3002")}}},
			nil)
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(&s3.ListObjectsV2Output{
			Contents: []types.Object{}},
			nil).AnyTimes()
		// オブジェクト取得が呼ばれない
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&s3.GetObjectOutput{}, nil).Times(0)
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

	t.Run("S3ディレクトリ一覧取得に失敗した時、処理が中断されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		defer func() {
			err := recover()
			if err != nil {
				t.Errorf("did not panic")
			}
		}()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)
		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(nil, errors.New("test error"))
		// ファイル取得が呼ばれない
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(&s3.ListObjectsV2Output{}, nil).Times(0)
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&s3.GetObjectOutput{}, nil).Times(0)
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

	t.Run("S3のオブジェクト一覧取得に失敗した時、処理が中断されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		defer func() {
			err := recover()
			if err != nil {
				t.Errorf("did not panic")
			}
		}()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)
		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(&s3.ListObjectsV2Output{
			CommonPrefixes: []types.CommonPrefix{
				{Prefix: aws.String("3000")},
				{Prefix: aws.String("3001")},
				{Prefix: aws.String("3002")}}},
			nil)
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(nil, errors.New("test error"))
		// オブジェクト取得が呼ばれない
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&s3.GetObjectOutput{}, nil).Times(0)
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

	t.Run("単一ゾーン分のS3ディレクトリ一覧とオブジェクト一覧と直下のオブジェクト取得が実行されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		defer func() { recover() }()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)

		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(&s3.ListObjectsV2Output{
			CommonPrefixes: []types.CommonPrefix{
				{Prefix: aws.String("3000")},
			}}, nil)
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(&s3.ListObjectsV2Output{
			Contents: []types.Object{
				{Key: aws.String("3000/test1.json")},
				{Key: aws.String("3000/3000/test1.json")},
				{Key: aws.String("3000/3000/3000/test1.json")},
				{Key: aws.String("3000/3000/3000/3000/test1.json")},
			}}, nil).AnyTimes()
		// オブジェクト取得が呼ばれる
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&s3.GetObjectOutput{}, nil).Times(1)
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

	t.Run("複数ゾーン分のS3ディレクトリ一覧とオブジェクト一覧と直下のオブジェクト取得が実行されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		defer func() { recover() }()

		repositoryMock := mock.NewMockS3AbiDaoRepository(ctrl)

		repositoryMock.EXPECT().ListCommonPrefixesObjects(gomock.Any()).Return(&s3.ListObjectsV2Output{
			CommonPrefixes: []types.CommonPrefix{
				{Prefix: aws.String("3000")},
				{Prefix: aws.String("3001")},
				{Prefix: aws.String("3002")},
			}}, nil)
		repositoryMock.EXPECT().ListObjects(gomock.Any(), gomock.Any()).Return(&s3.ListObjectsV2Output{
			Contents: []types.Object{
				{Key: aws.String("3000/test1.json")},
				{Key: aws.String("3000/3000/test1.json")},
				{Key: aws.String("3000/3000/3000/test1.json")},
				{Key: aws.String("3000/3000/3000/3000/test1.json")},
				{Key: aws.String("3001/test2.json")},
				{Key: aws.String("3001/3001/test2.json")},
				{Key: aws.String("3001/3001/3001/test2.json")},
				{Key: aws.String("3001/3001/3001/3001/test2.json")},
				{Key: aws.String("3003/test3.json")},
				{Key: aws.String("3003/3003/test3.json")},
				{Key: aws.String("3003/3003/3003/test3.json")},
				{Key: aws.String("3003/3003/3003/3003/test3.json")},
			}}, nil).AnyTimes()
		// オブジェクト取得が呼ばれる
		repositoryMock.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&s3.GetObjectOutput{}, nil).Times(1)
		// テスト実行
		interactor := NewDownloadAbiInteractor(l, repositoryMock)
		interactor.Execute()
	})

}
