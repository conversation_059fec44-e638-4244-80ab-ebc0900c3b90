package dynamodb

import (
	"context"
	"fmt"
	"sync"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/config"
)

type Table interface {
	Table() string
}

var sem = make(chan struct{}, 10) // 同時接続数制限

var clientPool = &sync.Pool{
	New: func() interface{} {
		var cfg aws.Config
		var err error
		switch config.GetEnvironment() {
		case "local":
			resolver := aws.EndpointResolverFunc(func(_, _0 string) (aws.Endpoint, error) {
				return aws.Endpoint{
					URL:           config.GetDynamoDBEndpoint(),
					SigningRegion: config.GetDynamoDBRegion(),
				}, nil
			})
			cfg, err = awsConfig.LoadDefaultConfig(context.TODO(), awsConfig.WithEndpointResolver(resolver))
		case "prod":
			cfg, err = awsConfig.LoadDefaultConfig(context.TODO())
		}
		if err != nil {
			return err
		}

		c := dynamodb.NewFromConfig(cfg)
		return c
	},
}

func newConnection() (*dynamodb.Client, error) {
	sem <- struct{}{}
	conn := clientPool.Get()

	if client, ok := conn.(*dynamodb.Client); ok {
		return client, nil
	} else if err, ok := conn.(error); ok {
		return nil, err
	} else {
		return nil, fmt.Errorf("unknown connection error")
	}
}

func releaseConnection(client *dynamodb.Client) {
	if client != nil {
		clientPool.Put(client)
	}
	select {
	case <-sem:
	default:
	}
}

func serializePutItemInput(tbl Table) (*dynamodb.PutItemInput, error) {
	av, err := attributevalue.MarshalMap(tbl)
	if err != nil {
		return nil, err
	}

	var tableName string
	tablePrefix := config.GetDynamoDBTablePrefix()
	if tablePrefix != "" {
		tableName = fmt.Sprintf("%s-%s", tablePrefix, tbl.Table())
	} else {
		tableName = tbl.Table()
	}

	input := dynamodb.PutItemInput{
		TableName: aws.String(tableName),
		Item:      av,
	}
	return &input, nil
}

func getItemInput(tbl Table, params dynamodb.QueryInput) *dynamodb.QueryInput {
	var tableName string
	tablePrefix := config.GetDynamoDBTablePrefix()
	if tablePrefix != "" {
		tableName = fmt.Sprintf("%s-%s", tablePrefix, tbl.Table())
	} else {
		tableName = tbl.Table()
	}
	params.TableName = aws.String(tableName)
	return &params
}
