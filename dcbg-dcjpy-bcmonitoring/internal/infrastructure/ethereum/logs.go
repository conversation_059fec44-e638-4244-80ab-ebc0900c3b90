package ethereum

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"strconv"
	"time"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/config"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/sirupsen/logrus"
)

var (
	// time.Now関数をテスタブルにするため関数をWrap
	timeNow         = time.Now
	retryWaitSecond = 3 * time.Second
)

type EthEventLogDao struct {
	context context.Context
	l       *logrus.Entry
	client  *ethclient.Client
}

func NewEventLogDao(ctx context.Context, l *logrus.Entry) (*EthEventLogDao, error) {
	client, err := newWSClient(ctx)
	if err != nil {
		return nil, err
	}
	return &EthEventLogDao{context: ctx, l: l, client: client}, nil
}

func (dao *EthEventLogDao) SubscribeAll(ctx context.Context) (<-chan entity.Transaction, error) {
	headers := make(chan *types.Header)
	subscribe, err := dao.client.SubscribeNewHead(ctx, headers)
	transactions := make(chan entity.Transaction)
	if err != nil {
		dao.l.Errorln(err)
		time.Sleep(retryWaitSecond)
		return nil, err
	}
	allowableDiff, err := strconv.Atoi(config.GetAllowableBlockTimestampDIffSec())
	if err != nil {
		return nil, err
	}
	go func() {
		defer close(transactions)
		defer dao.l.Infoln("subscribe is done")

		dao.l.Infoln("start subscribe event")

		for {
			select {
			case <-ctx.Done():
				dao.l.Infof("context done due to cancel or timeout")
				return
			case err := <-subscribe.Err():
				dao.l.Errorln(err)
				return
			case header := <-headers:
				block, err := dao.client.BlockByNumber(context.Background(), header.Number)
				if err != nil {
					dao.l.Errorln(err)
					return
				}
				if delayed, diff, timeNow := dao.isDelayedToDetectBlockHeader(block.Time(), uint64(allowableDiff)); delayed {
					dao.l.WithFields(logrus.Fields{
						"block_detected_timestamp": timeNow,
						"block_created_timestamp":  block.Time(),
						"block_number":             header.Number,
					}).Warnln(fmt.Sprintf("delay for detecting event for %v sec", diff))
				}
				es, err := dao.convBlock2EventEntities(dao.context, block)
				if err != nil {
					if !errors.Is(err, ErrNoAbiEventFound) {
						dao.l.Errorln(err)
					}
					return
				}
				if es != nil {
					dao.l.WithField("block_number", header.Number).Infoln("detect block includes events")
					transactions <- entity.Transaction{
						Events: es,
						BlockHeight: entity.BlockHeight{
							BlockNumber: header.Number.Uint64(),
						},
					}
				}
			}
		}
	}()
	return transactions, nil
}

func (dao *EthEventLogDao) convBlock2EventEntities(ctx context.Context, block *types.Block) (entity.Events, error) {
	var es entity.Events
	for _, tx := range block.Transactions() {
		receipt, err := dao.client.TransactionReceipt(context.Background(), tx.Hash())
		if err != nil {
			return nil, err
		}
		for _, log := range receipt.Logs {
			dao.l.WithField("tx_hash", log.TxHash).Infoln("event found")
			event, err := dao.convertEthLogToEventEntity(ctx, *log)
			if err != nil {
				return nil, err
			}
			dao.l.WithFields(logrus.Fields{"tx_hash": event.TransactionHash, "name": event.Name}).Infoln("event parsed")
			if event.TransactionHash != "" {
				es = append(es, event)
			}
		}
	}
	return es, nil
}

func (dao *EthEventLogDao) isDelayedToDetectBlockHeader(blockCreatedTimestamp, allowableSec uint64) (bool, uint64, uint64) {
	timeNow := uint64(timeNow().Unix())

	// blockCreatedTimestampはミリ秒以下を四捨五入した値であり、timeNowよ1秒進んでいる可能性がある
	// 上記を考慮してblockCreatedTimestamp > timeNowとなった場合はブロック検知遅延していない状態として扱う
	// https://decurret.atlassian.net/wiki/spaces/DIG/pages/2547450394/BCMonitoring+Warning
	if blockCreatedTimestamp > timeNow {
		return false, 0, timeNow
	}
	diffSec := timeNow - blockCreatedTimestamp
	return diffSec > allowableSec, diffSec, timeNow
}

// EthereumのログをEventエンティティに変換する
func (dao *EthEventLogDao) convertEthLogToEventEntity(ctx context.Context, ethLog types.Log) (entity.Event, error) {
	block, err := dao.client.BlockByNumber(ctx, new(big.Int).SetUint64(ethLog.BlockNumber))
	if err != nil {
		return entity.Event{}, err
	}

	abiEvent, err := getABIEventByLog(ethLog)
	if err != nil {
		if errors.Is(err, ErrNoAbiEventFound) {
			dao.l.WithFields(logrus.Fields{
				"event_id": ethLog.Topics[0].String(),
				"address":  ethLog.Address.String(),
			}).Infoln(ErrNoAbiEventFound)
		}
		return entity.Event{}, err
	}

	// ログのtopicsからindexedあり引数を取得する
	var indexedInputs abi.Arguments
	for _, arg := range abiEvent.Inputs {
		if arg.Indexed {
			indexedInputs = append(indexedInputs, arg)
		}
	}
	indexedValues := make(map[string]interface{}, len(indexedInputs))
	if err := abi.ParseTopicsIntoMap(indexedValues, indexedInputs, ethLog.Topics[1:]); err != nil {
		return entity.Event{}, err
	}
	indexedJson, err := json.Marshal(indexedValues)
	if err != nil {
		return entity.Event{}, err
	}

	// ログのdataからindexedなし引数を取得する
	nonIndexedValues := make(map[string]interface{})
	if err := abiEvent.Inputs.NonIndexed().UnpackIntoMap(nonIndexedValues, ethLog.Data); err != nil {
		return entity.Event{}, err
	}
	nonIndexedJson, err := json.Marshal(nonIndexedValues)
	if err != nil {
		return entity.Event{}, err
	}

	// ログのパース
	logJson, err := ethLog.MarshalJSON()
	if err != nil {
		return entity.Event{}, err
	}

	return entity.Event{
		Name:             abiEvent.Name,
		TransactionHash:  ethLog.TxHash.String(),
		LogIndex:         ethLog.Index,
		IndexedValues:    string(indexedJson),
		NonIndexedValues: string(nonIndexedJson),
		BlockTimestamp:   block.Time(),
		Log:              string(logJson),
	}, nil
}

func (dao *EthEventLogDao) GetPendingTransactions(ctx context.Context, blockHeight uint64) (<-chan entity.Transaction, error) {
	logs := make(chan types.Log)
	done := make(chan struct{})
	transactions := make(chan entity.Transaction)

	dao.l.Infof("blockHeight: %d", blockHeight)

	filterLogs, err := dao.client.FilterLogs(ctx, filterFrom(int64(blockHeight)))
	if err != nil {
		dao.l.Errorln(err)
		time.Sleep(retryWaitSecond)
		return nil, err
	}

	go func(done chan struct{}) {
		defer close(transactions)
		defer dao.l.Infoln("pending transactions is done")
		for {
			select {
			case <-ctx.Done():
				dao.l.Infof("context done due to cancel or timeout")
				return
			case l := <-logs:
				dao.l.WithField("tx_hash", l.TxHash).Infoln("event found")
				e, err := dao.convertEthLogToEventEntity(dao.context, l)
				if err != nil {
					if !errors.Is(err, ErrNoAbiEventFound) {
						dao.l.Errorln(err)
					}
					return
				}
				dao.l.WithFields(logrus.Fields{"tx_hash": e.TransactionHash, "name": e.Name}).Infoln("event parsed")
				transactions <- entity.Transaction{
					Events: entity.Events{e},
					BlockHeight: entity.BlockHeight{
						BlockNumber: l.BlockNumber,
					},
				}
			case <-done:
				return
			}
		}
	}(done)

	go func(transactions []types.Log) {
		for _, t := range transactions {
			logs <- t
		}
		close(done)
	}(filterLogs)

	return transactions, nil
}
