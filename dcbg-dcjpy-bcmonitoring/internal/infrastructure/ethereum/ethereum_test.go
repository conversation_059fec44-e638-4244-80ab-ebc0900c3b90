package ethereum

import (
	"errors"
	"reflect"
	"testing"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

func Test_getABIEventByLog(t *testing.T) {
	type args struct {
		log types.Log
	}
	tests := []struct {
		name    string
		args    args
		want    abi.Event
		wantErr bool
		errKind error
		pre     func()
	}{
		{
			name: "should return error of no_abi_event_found",
			args: args{log: types.Log{
				Address: common.Address{},
				Topics:  []common.Hash{{}},
			}},
			want:    abi.Event{},
			wantErr: true,
			errKind: ErrNoAbiEventFound,
		},
		{
			name: "should return proper abi.Event",
			args: args{log: types.Log{
				Address: common.Address{238, 201, 24, 215, 76, 116, 97, 103, 86, 68, 1, 16, 48, 150, 212, 91, 189, 73, 75, 116},
				Topics:  []common.Hash{{40, 144, 67, 45, 165, 16, 52, 215, 218, 250, 3, 212, 147, 185, 127, 31, 110, 111, 91, 143, 239, 234, 203, 113, 248, 87, 209, 134, 128, 229, 105, 198}},
			}},
			want: abi.Event{Name: "AddIssuer", ID: common.Hash{40, 144, 67, 45, 165, 16, 52, 215, 218, 250, 3, 212, 147, 185, 127, 31, 110, 111, 91, 143, 239, 234, 203, 113, 248, 87, 209, 134, 128, 229, 105, 198}},
			pre: func() {
				AppendContractEventStore("0xeec918d74c746167564401103096d45bbd494b74", "AddIssuer", map[string]abi.Event{
					"AddIssuer": {
						Name: "AddIssuer",
						ID:   common.Hash{40, 144, 67, 45, 165, 16, 52, 215, 218, 250, 3, 212, 147, 185, 127, 31, 110, 111, 91, 143, 239, 234, 203, 113, 248, 87, 209, 134, 128, 229, 105, 198},
					},
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.pre != nil {
				tt.pre()
			}
			got, err := getABIEventByLog(tt.args.log)
			if (err != nil) != tt.wantErr {
				t.Errorf("getABIEventByLog() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !errors.Is(err, tt.errKind) {
				t.Errorf("getABIEventByLog() error = %v, errKind %v", err, tt.errKind)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getABIEventByLog() got = %#v, want %#v", got, tt.want)
			}
		})
	}
}
