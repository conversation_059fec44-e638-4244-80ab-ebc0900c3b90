package s3

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/sirupsen/logrus"
)

const FILEPATH_DELIMITER = "/"

type S3AbiDao struct {
	l *logrus.Entry
}

func NewS3AbiDao(l *logrus.Entry) *S3AbiDao {
	return &S3AbiDao{l}
}

func (dao *S3AbiDao) ListCommonPrefixesObjects(bucketName string) (*s3.ListObjectsV2Output, error) {
	client, err := newS3Client()
	if err != nil {
		return nil, err
	}
	return client.ListObjectsV2(context.TODO(), &s3.ListObjectsV2Input{
		Bucket:    aws.String(bucketName),
		Delimiter: aws.String(FILEPATH_DELIMITER),
	})
}

func (dao *S3AbiDao) ListObjects(bucketName string, prefix string) (*s3.ListObjectsV2Output, error) {
	client, err := newS3Client()
	if err != nil {
		return nil, err
	}
	return client.ListObjectsV2(context.TODO(), &s3.ListObjectsV2Input{
		Bucket: aws.String(bucketName),
		Prefix: aws.String(prefix),
	})
}

func (dao *S3AbiDao) GetObject(bucketName string, key string) (*s3.GetObjectOutput, error) {
	client, err := newS3Client()
	if err != nil {
		return nil, err
	}
	return client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
}
