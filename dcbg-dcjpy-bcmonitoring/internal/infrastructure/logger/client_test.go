package logger

import (
	"bytes"
	"encoding/json"
	"github.com/sirupsen/logrus"
	"strings"
	"testing"
)

func TestShouldBePlainFormat(t *testing.T) {
	var buf bytes.Buffer
	defer func() {
		buf.Reset()
	}()
	l := NewLogger("test")
	l.<PERSON>gger.SetOutput(&buf)
	l.Lo<PERSON>.SetLevel(logrus.InfoLevel)
	l.Infoln("testing...")

	var js json.RawMessage
	err := json.Unmarshal([]byte(buf.String()), &js)
	if err == nil {
		t.Errorf("logging format should not be json")
	}
}

func TestShouldHaveMetrics(t *testing.T) {
	var buf bytes.Buffer
	tests := []struct {
		name  string
		want  string
		level logrus.Level
	}{
		{
			name:  "[info]should have uuid",
			want:  "uuid",
			level: logrus.InfoLevel,
		},
		{
			name:  "[info]should have msg",
			want:  "msg",
			level: logrus.InfoLevel,
		},
		{
			name:  "[info]should have time",
			want:  "time",
			level: logrus.InfoLevel,
		},
		{
			name:  "[info]should have file",
			want:  "file",
			level: logrus.InfoLevel,
		},
		{
			name:  "[info]should have level",
			want:  "level",
			level: logrus.InfoLevel,
		},
		{
			name:  "[error]should have uuid",
			want:  "uuid",
			level: logrus.ErrorLevel,
		},
		{
			name:  "[error]should have msg",
			want:  "msg",
			level: logrus.ErrorLevel,
		},
		{
			name:  "[error]should have time",
			want:  "time",
			level: logrus.ErrorLevel,
		},
		{
			name:  "[error]should have file",
			want:  "file",
			level: logrus.ErrorLevel,
		},
		{
			name:  "[error]should have level",
			want:  "level",
			level: logrus.ErrorLevel,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer func() {
				buf.Reset()
			}()
			l := NewLogger("test")
			l.Logger.SetLevel(tt.level)
			l.Logger.SetOutput(&buf)
			if tt.level == logrus.InfoLevel {
				l.Infoln("test")
			} else if tt.level == logrus.ErrorLevel {
				l.Errorln("test")
			} else {
				t.Errorf("log level should be defined")
			}
			got := buf.String()
			if !strings.Contains(got, tt.want) {
				t.Errorf("got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestShouldBePanicked(t *testing.T) {
	defer func() {
		if err := recover(); err == nil {
			t.Errorf("The code did not panic")
		}
	}()
	l := NewLogger("test")
	l.Logger.SetOutput(nil)
	l.Panicln("panic")
}
