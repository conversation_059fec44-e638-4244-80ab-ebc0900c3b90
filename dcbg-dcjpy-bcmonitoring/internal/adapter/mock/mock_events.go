// Code generated by MockGen. DO NOT EDIT.
// Source: events.go
//
// Generated by this command:
//
//	mockgen -source=events.go -package=mock -destination=./mock/mock_events.go
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	entity "github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockEventDao is a mock of EventDao interface.
type MockEventDao struct {
	ctrl     *gomock.Controller
	recorder *MockEventDaoMockRecorder
}

// MockEventDaoMockRecorder is the mock recorder for MockEventDao.
type MockEventDaoMockRecorder struct {
	mock *MockEventDao
}

// NewMockEventDao creates a new mock instance.
func NewMockEventDao(ctrl *gomock.Controller) *MockEventDao {
	mock := &MockEventDao{ctrl: ctrl}
	mock.recorder = &MockEventDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEventDao) EXPECT() *MockEventDaoMockRecorder {
	return m.recorder
}

// PutItem mocks base method.
func (m *MockEventDao) PutItem(arg0 entity.Event) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutItem", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutItem indicates an expected call of PutItem.
func (mr *MockEventDaoMockRecorder) PutItem(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutItem", reflect.TypeOf((*MockEventDao)(nil).PutItem), arg0)
}
