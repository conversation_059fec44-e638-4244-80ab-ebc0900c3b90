// Code generated by MockGen. DO NOT EDIT.
// Source: block_height.go
//
// Generated by this command:
//
//	mockgen -source=block_height.go -package=mock -destination=./mock/mock_block_height.go
//

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	entity "github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	gomock "go.uber.org/mock/gomock"
)

// MockBlockHeightDao is a mock of BlockHeightDao interface.
type MockBlockHeightDao struct {
	ctrl     *gomock.Controller
	recorder *MockBlockHeightDaoMockRecorder
}

// MockBlockHeightDaoMockRecorder is the mock recorder for MockBlockHeightDao.
type MockBlockHeightDaoMockRecorder struct {
	mock *MockBlockHeightDao
}

// NewMockBlockHeightDao creates a new mock instance.
func NewMockBlockHeightDao(ctrl *gomock.Controller) *MockBlockHeightDao {
	mock := &MockBlockHeightDao{ctrl: ctrl}
	mock.recorder = &MockBlockHeightDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBlockHeightDao) EXPECT() *MockBlockHeightDaoMockRecorder {
	return m.recorder
}

// GetItem mocks base method.
func (m *MockBlockHeightDao) GetItem() ([]entity.BlockHeight, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItem")
	ret0, _ := ret[0].([]entity.BlockHeight)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItem indicates an expected call of GetItem.
func (mr *MockBlockHeightDaoMockRecorder) GetItem() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItem", reflect.TypeOf((*MockBlockHeightDao)(nil).GetItem))
}

// PutItem mocks base method.
func (m *MockBlockHeightDao) PutItem(arg0 entity.BlockHeight) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutItem", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutItem indicates an expected call of PutItem.
func (mr *MockBlockHeightDaoMockRecorder) PutItem(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutItem", reflect.TypeOf((*MockBlockHeightDao)(nil).PutItem), arg0)
}
