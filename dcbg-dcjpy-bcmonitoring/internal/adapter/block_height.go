//go:generate mockgen -source=$GOFILE -package=mock -destination=./mock/mock_$GOFILE
package adapter

import (
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"github.com/sirupsen/logrus"
)

type BlockHeightDao interface {
	PutItem(entity.BlockHeight) error
	GetItem() ([]entity.BlockHeight, error)
}

type BlockHeightRepository struct {
	l              *logrus.Entry
	blockHeightDao BlockHeightDao
}

func NewBlockHeightRepository(l *logrus.Entry, blockHeightDao BlockHeightDao) *BlockHeightRepository {
	return &BlockHeightRepository{l, blockHeightDao}
}

func (repo *BlockHeightRepository) Save(blockHeight entity.BlockHeight) bool {
	err := repo.blockHeightDao.PutItem(blockHeight)
	if err != nil {
		repo.l.Errorln(err)
		return false
	}
	return true
}

func (repo *BlockHeightRepository) Get() (entity.BlockHeight, error) {
	blockHeight, err := repo.blockHeightDao.GetItem()
	if err != nil {
		repo.l.Errorln(err)
		return entity.BlockHeight{}, err
	}
	if len(blockHeight) == 0 {
		return entity.BlockHeight{
			BlockNumber: 0,
		}, nil
	}
	return blockHeight[0], nil
}
