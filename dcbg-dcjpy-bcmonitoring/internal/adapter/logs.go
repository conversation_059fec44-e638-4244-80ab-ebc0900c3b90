//go:generate mockgen -source=$GOFILE -package=mock -destination=./mock/mock_$GOFILE
package adapter

import (
	"context"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"github.com/sirupsen/logrus"
)

type EventLogDao interface {
	SubscribeAll(context.Context) (<-chan entity.Transaction, error)
	GetPendingTransactions(context.Context, uint64) (<-chan entity.Transaction, error)
}

type EventLogRepository struct {
	l           *logrus.Entry
	eventLogDao EventLogDao
}

func NewEventLogRepository(l *logrus.Entry, eventLogDao EventLogDao) *EventLogRepository {
	return &EventLogRepository{l, eventLogDao}
}

func (repo *EventLogRepository) Subscribe(ctx context.Context) (<-chan entity.Transaction, error) {
	if transactions, err := repo.eventLogDao.SubscribeAll(ctx); err == nil {
		return transactions, nil
	} else {
		repo.l.Errorln(err)
		return nil, err
	}
}

func (repo *EventLogRepository) GetFilterLogs(ctx context.Context, blockHeight uint64) (<-chan entity.Transaction, error) {
	if transactions, err := repo.eventLogDao.GetPendingTransactions(ctx, blockHeight); err == nil {
		return transactions, nil
	} else {
		repo.l.Errorln(err)
		return nil, err
	}
}
