package adapter

import (
	"fmt"
	"testing"

	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/adapter/mock"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/entity"
	"github.com/decurret-lab/dcbg-dcf-bcmonitoring-sandbox/internal/infrastructure/logger"
	"go.uber.org/mock/gomock"
)

func TestEventRepository_Save(t *testing.T) {
	l := logger.NewLogger("test")
	t.Run("保存処理を実行したとき、DAOが一度だけ呼び出されること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		event := entity.Event{Name: "name"}

		mockEventDao := mock.NewMockEventDao(ctrl)
		mockEventDao.EXPECT().PutItem(gomock.Any()).Do(func(e entity.Event) {
			if event.Name != e.Name {
				t.Errorf("invalid event is given, expect %s / actual %s\n", event.Name, e.Name)
			}
		}).Times(1)

		eventRepository := NewEventRepository(l, mockEventDao)
		if !eventRepository.Save(event) {
			t.Errorf("test failed: fail to save event")
		}
	})

	t.Run("保存処理に失敗した場合、falseが返ること", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		event := entity.Event{Name: "name"}

		mockEventDao := mock.NewMockEventDao(ctrl)
		mockEventDao.EXPECT().PutItem(event).Return(fmt.Errorf("error mock")).Times(1)

		eventRepository := NewEventRepository(l, mockEventDao)
		if eventRepository.Save(event) {
			t.Errorf("test failed: success to save event\n")
		}
	})
}
