# dcbg-dcf-bcmonitoring-sandbox

BCモニタリングはEthereumクライアントで発生したイベントを取得し、そのデータを加工した上でデータベースに保存する役割を持つアプリケーションです。  
起動後、イベントの取得を自動的に開始します。

## 構成

[こちら](https://decurret.atlassian.net/wiki/spaces/DIG/pages/1834713267/BCMonitoring)を参照ください。

## ライブラリ情報

[こちら](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2249883665/BCMonitoring#BCMonitoring)を参照ください。

ライブラリの追加/変更作業時にはドキュメントの更新が必要です。

## 環境変数一覧

[環境変数一覧(BCMonitoring Web3Stream RDSStream](https://decurret.atlassian.net/wiki/spaces/DIG/pages/2440921430/BCMonitoring+Web3Stream+RDSStream#BCMonitoring)


## ローカルでの起動方法

[こちら](https://decurret.atlassian.net/wiki/spaces/DIG/pages/1841857371/BCMonitoring)を参照ください。

```shell
% sh ./scripts/run-local.sh
```

Hot reload（air）を導入しているため、拡張子が「.go」のファイルを編集/保存した場合、 ソースコードの変更がDocker上のプロセスに即時反映されます。

## リモートデバッグ

上記「ローカルでの起動方法」で立ち上げたプロセスは、Golangのデバッガ（delve）で起動しており、 ご利用のエディタで「localhost:2345」に接続することで、Docker上のプロセスをリモートデバッグすることができます。

## ローカルでのテスト

```shell
% sh ./scripts/run-test.sh
```
