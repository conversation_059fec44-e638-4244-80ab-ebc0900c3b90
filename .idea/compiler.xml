<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Gradle Imported" enabled="true">
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.36/5a30490a6e14977d97d9c73c924c1f1b5311ea95/lombok-1.18.36.jar" />
        </processorPath>
        <module name="dcbg-dcjpy-bcmonitoring.main" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="dcbg-dcjpy-bcmonitoring" options="-parameters" />
      <module name="dcbg-dcjpy-bcmonitoring.main" options="-parameters" />
      <module name="dcbg-dcjpy-bcmonitoring.test" options="-parameters" />
    </option>
  </component>
</project>