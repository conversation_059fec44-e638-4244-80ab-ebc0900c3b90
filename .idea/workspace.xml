<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="41767504-50a0-4f60-b255-4658e811d8e2" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/base/BaseAdhocIntegrationSpec.groovy" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dcbg-dcjpy-bcclient/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcclient/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dcbg-dcjpy-bcclient/docker-compose-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcclient/docker-compose-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/abi/DownloadAbiServiceIntegrationSpec.groovy" beforeDir="false" afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/abi/DownloadAbiServiceIntegrationSpec.groovy" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/helper/AdhocHelper.groovy" beforeDir="false" afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/helper/AdhocHelper.groovy" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/startup/StartupServiceSpec.groovy" beforeDir="false" afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/startup/StartupServiceSpec.groovy" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring/docker-compose-local.yml" beforeDir="false" afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring/docker-compose-local.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring/docker-compose.yml" beforeDir="false" afterPath="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring/docker-compose.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring-java">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="dcbg-dcjpy-bcmonitoring" type="f1a62948:ProjectNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/dcbg-dcjpy-bcmonitoring" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="2yqd2WK917lznLWEem2TdFvfa7Z" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "develop",
    "last_opened_file_path": "/Users/<USER>/Documents/My_Work_Space/BCMAdhoc"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="41767504-50a0-4f60-b255-4658e811d8e2" name="Changes" comment="" />
      <created>1750565830170</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750565830170</updated>
    </task>
    <servers />
  </component>
</project>