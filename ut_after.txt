./gradlew clean check testAdhoc

> Task :compileJava
ノート: 入力ファイルの操作のうち、未チェックまたは安全ではないものがあります。
ノート: 詳細は、-Xlint:uncheckedオプションを指定して再コンパイルしてください。
OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended

> Task :test

should successfully get block height STARTED

should successfully get block height PA<PERSON><PERSON>

should return 0 when no block heights found STARTED

should return 0 when no block heights found PA<PERSON><PERSON>

should throw DataAccessException when get fails STARTED

should throw DataAccessException when get fails PASSED

should successfully save block height STARTED

should successfully save block height PASSED

should handle exception when saving block height fails STARTED

should handle exception when saving block height fails PASSED

should return true when saving null block height STARTED

should return true when saving null block height PASSED

should handle generic Exception during get operation STARTED

should handle generic Exception during get operation PASSED

should handle generic Exception during save operation STARTED

should handle generic Exception during save operation PASSED

should successfully save an event STARTED

should successfully save an event PA<PERSON>ED

should handle exception when saving event fails STARTED

should handle exception when saving event fails PA<PERSON>ED

should return true when handling null event STARTED

should return true when handling null event <PERSON><PERSON><PERSON>

should attempt to save event even with empty attribute map STARTED

should attempt to save event even with empty attribute map PASSED

should handle generic Exception during save operation STARTED

should handle generic Exception during save operation PA<PERSON>ED

should successfully execute operation with connection STARTED

should successfully execute operation with connection PASSED

should handle InterruptedException during connection acquisition STARTED

should handle InterruptedException during connection acquisition PASSED

should handle DynamoDbException during operation execution STARTED

should handle DynamoDbException during operation execution PASSED

should handle generic Exception during operation execution STARTED

should handle generic Exception during operation execution PASSED

should release connection even when operation throws exception STARTED

should release connection even when operation throws exception PASSED

should not release connection when acquisition fails STARTED

should not release connection when acquisition fails PASSED

should handle null client gracefully in finally block STARTED

should handle null client gracefully in finally block PASSED

should execute operation with proper return type STARTED

should execute operation with proper return type PASSED

should execute operation that returns null STARTED

should execute operation that returns null PASSED

should preserve thread interruption status when InterruptedException occurs STARTED

should preserve thread interruption status when InterruptedException occurs PASSED

should handle multiple operations with same service instance STARTED

should handle multiple operations with same service instance PASSED

should handle operation that modifies client state STARTED

should handle operation that modifies client state PASSED

should handle nested exception in operation STARTED

should handle nested exception in operation PASSED

convBlock2EventEntities should handle empty transaction lists STARTED

convBlock2EventEntities should handle empty transaction lists PASSED

convBlock2EventEntities should handle missing transaction receipts STARTED

convBlock2EventEntities should handle missing transaction receipts PASSED

convBlock2EventEntities should process transactions with valid logs STARTED

convBlock2EventEntities should process transactions with valid logs PASSED

convBlock2EventEntities should include events with null transaction hash STARTED

convBlock2EventEntities should include events with null transaction hash PASSED

convBlock2EventEntities should handle exceptions during log processing STARTED

convBlock2EventEntities should handle exceptions during log processing PASSED

convBlock2EventEntities should handle exceptions during transaction processing STARTED

convBlock2EventEntities should handle exceptions during transaction processing PASSED

convBlock2EventEntities should include events with empty transaction hash STARTED

convBlock2EventEntities should include events with empty transaction hash PASSED

getPendingTransactions should handle exceptions during event processing STARTED

getPendingTransactions should handle exceptions during event processing PASSED

isDelayed should detect delayed blocks STARTED

isDelayed should detect delayed blocks PASSED

decodeTupleArray should handle regular array inside tuple array STARTED

decodeTupleArray should handle regular array inside tuple array PASSED

getPendingTransactions should process logs and return transactions STARTED

getPendingTransactions should process logs and return transactions PASSED

getPendingTransactions should process logs with valid data STARTED

getPendingTransactions should process logs with valid data PASSED

getPendingTransactions should handle log processing errors STARTED

getPendingTransactions should handle log processing errors PASSED

getPendingTransactions should handle general log processing errors STARTED

getPendingTransactions should handle general log processing errors PASSED

getPendingTransactions should handle exceptions STARTED

getPendingTransactions should handle exceptions PASSED

getPendingTransactions should log 'Error processing pending transactions' when forceOuterError is true STARTED

getPendingTransactions should log 'Error processing pending transactions' when forceOuterError is true PASSED

getPendingTransactions should process a log entry correctly STARTED

getPendingTransactions should process a log entry correctly PASSED

should get block timestamp correctly STARTED

should get block timestamp correctly PASSED

subscribeAll should subscribe to contract events STARTED

subscribeAll should subscribe to contract events PASSED

subscribeAll should subscribe to block events STARTED

subscribeAll should subscribe to block events PASSED

subscribeAll should skip processing for delayed blocks STARTED

subscribeAll should skip processing for delayed blocks PASSED

subscribeAll should process non-delayed blocks with events STARTED

subscribeAll should process non-delayed blocks with events PASSED

subscribeAll should handle exceptions during block processing with events STARTED

subscribeAll should handle exceptions during block processing with events PASSED

subscribeAll should events is empty when processing with events STARTED

subscribeAll should events is empty when processing with events PASSED

subscribeAll should add transaction to queue when events are found STARTED

subscribeAll should add transaction to queue when events are found PASSED

subscribeAll should not add transaction to queue when no events are found STARTED

subscribeAll should not add transaction to queue when no events are found PASSED

convertEthLogToEventEntity should successfully convert a log to an event with ABI event STARTED

convertEthLogToEventEntity should successfully convert a log to an event with ABI event PASSED

convertEthLogToEventEntity should failed convert a log with EventValues is null  STARTED

convertEthLogToEventEntity should failed convert a log with EventValues is null  PASSED

convertEthLogToEventEntity should handle null ABI event STARTED

convertEthLogToEventEntity should handle null ABI event PASSED

convertEthLogToEventEntity should handle block retrieval exception STARTED

convertEthLogToEventEntity should handle block retrieval exception PASSED

convertEthLogToEventEntity should handle ABI parser exception STARTED

convertEthLogToEventEntity should handle ABI parser exception PASSED

convertEthLogToEventEntity should handle empty topics list STARTED

convertEthLogToEventEntity should handle empty topics list PASSED

convBlock2EventEntities should process events from a block with logs STARTED

convBlock2EventEntities should process events from a block with logs PASSED

subscribeAll should handle NumberFormatException when parsing allowable timestamp difference STARTED

subscribeAll should handle NumberFormatException when parsing allowable timestamp difference PASSED

subscribeAll should log subscription error STARTED

subscribeAll should log subscription error PASSED

subscribeAll should handle exception during Web3j subscription creation STARTED

subscribeAll should handle exception during Web3j subscription creation PASSED

unsubscribe should dispose subscription when subscription is not null STARTED

unsubscribe should dispose subscription when subscription is not null PASSED

unsubscribe should handle null subscription gracefully STARTED

unsubscribe should handle null subscription gracefully PASSED

getBlockTimestamp should return correct timestamp STARTED

getBlockTimestamp should return correct timestamp PASSED

getBlockTimestamp should handle IOException STARTED

getBlockTimestamp should handle IOException PASSED

convBlock2EventEntities should handle Web3j creation exception STARTED

convBlock2EventEntities should handle Web3j creation exception PASSED

convertEthLogToEventEntity should handle general exceptions during processing STARTED

convertEthLogToEventEntity should handle general exceptions during processing PASSED

subscribeAll should handle subscription callback with delayed block STARTED

subscribeAll should handle subscription callback with delayed block PASSED

subscribeAll should handle subscription callback with non-delayed block and events STARTED

subscribeAll should handle subscription callback with non-delayed block and events PASSED

subscribeAll should handle subscription callback with empty events STARTED

subscribeAll should handle subscription callback with empty events PASSED

subscribeAll should handle subscription callback exception during block processing STARTED

subscribeAll should handle subscription callback exception during block processing PASSED

subscribeAll should handle subscription completion STARTED

subscribeAll should handle subscription completion PASSED

subscribeAll should execute subscription callback and process block STARTED

subscribeAll should execute subscription callback and process block PASSED

subscribeAll should execute subscription callback and add transaction to queue STARTED

subscribeAll should execute subscription callback and add transaction to queue PASSED

subscribeAll should handle exception in subscription callback STARTED

subscribeAll should handle exception in subscription callback PASSED

subscribeAll should handle empty events in subscription callback STARTED

subscribeAll should handle empty events in subscription callback PASSED

subscribeAll should handle null events in subscription callback STARTED

subscribeAll should handle null events in subscription callback PASSED

subscribeAll should trigger main subscription callback lambda0 STARTED

subscribeAll should trigger main subscription callback lambda0 PASSED

subscribeAll should trigger InterruptedException in async callback STARTED

subscribeAll should trigger InterruptedException in async callback PASSED

subscribeAll should trigger subscription error callback lambda3 STARTED

subscribeAll should trigger subscription error callback lambda3 PASSED

subscribeAll should handle error callback with proper cleanup STARTED

subscribeAll should handle error callback with proper cleanup PASSED

subscribeAll should trigger main subscription callback lambda0 with real execution STARTED

subscribeAll should trigger main subscription callback lambda0 with real execution PASSED

subscribeAll should cover remaining async callback paths with forced execution STARTED

subscribeAll should cover remaining async callback paths with forced execution PASSED

convBlock2EventEntities should handle null transaction STARTED

convBlock2EventEntities should handle null transaction PASSED

getPendingTransactions should handle forced outer error STARTED

getPendingTransactions should handle forced outer error PASSED

convertEthLogToEventEntity should handle real AddProviderRole event with bytes32 parameters STARTED

convertEthLogToEventEntity should handle real AddProviderRole event with bytes32 parameters PASSED

decodeTuple should decode simple tuple with basic types STARTED

decodeTuple should decode simple tuple with basic types PASSED

decodeTuple should handle nested tuple types STARTED

decodeTuple should handle nested tuple types PASSED

decodeTuple should handle DynamicArray in tuple STARTED

decodeTuple should handle DynamicArray in tuple PASSED

decodeTupleArray should handle empty components list STARTED

decodeTupleArray should handle empty components list PASSED

decodeDynamicArray should decode array of Type objects STARTED

decodeDynamicArray should decode array of Type objects PASSED

getComponentValue should extract value from DynamicStruct STARTED

getComponentValue should extract value from DynamicStruct PASSED

getComponentValue should extract value from StaticStruct STARTED

getComponentValue should extract value from StaticStruct PASSED

getComponentValue should throw RuntimeException when values are empty STARTED

getComponentValue should throw RuntimeException when values are empty PASSED

decodeEventParameters should handle tuple type parameters STARTED

decodeEventParameters should handle tuple type parameters PASSED

decodeEventParameters should handle tuple array type parameters STARTED

decodeEventParameters should handle tuple array type parameters PASSED

decodeEventParameters should handle empty event values STARTED

decodeEventParameters should handle empty event values PASSED

decodeEventParameters should handle dynamic array type parameters STARTED

decodeEventParameters should handle dynamic array type parameters PASSED

decodeEventParameters should handle basic type parameters STARTED

decodeEventParameters should handle basic type parameters PASSED

decodeTupleArray should handle DynamicArray in tuple array STARTED

decodeTupleArray should handle DynamicArray in tuple array PASSED

decodeTupleArray should handle nested tuple array in tuple array STARTED

decodeTupleArray should handle nested tuple array in tuple array PASSED

decodeEventParameters should handle more inputs than values STARTED

decodeEventParameters should handle more inputs than values PASSED

convertEthLogToEventEntity should test ObjectMapper serialization functionality STARTED

convertEthLogToEventEntity should test ObjectMapper serialization functionality PASSED

convertEthLogToEventEntity should successfully create Event object with real AddProviderRole event STARTED

convertEthLogToEventEntity should successfully create Event object with real AddProviderRole event PASSED

subscribeAll should call convBlock2EventEntities when block has transactions STARTED

subscribeAll should call convBlock2EventEntities when block has transactions PASSED

subscribeAll should log 'detect block includes events' when convBlock2EventEntities returns events STARTED

subscribeAll should log 'detect block includes events' when convBlock2EventEntities returns events PASSED

Subscribe should return transactions when successful STARTED

Subscribe should return transactions when successful PASSED

Subscribe should throw BlockchainException when DAO error occurs STARTED

Subscribe should throw BlockchainException when DAO error occurs PASSED

GetFilterLogs should return transactions when successful STARTED

GetFilterLogs should return transactions when successful PASSED

GetFilterLogs should throw BlockchainException when DAO error occurs STARTED

GetFilterLogs should throw BlockchainException when DAO error occurs PASSED

constructor with 3 parameters should set fields correctly STARTED

constructor with 3 parameters should set fields correctly PASSED

constructor with 4 parameters should set fields correctly STARTED

constructor with 4 parameters should set fields correctly PASSED

constructor should handle null components STARTED

constructor should handle null components PASSED

constructor should handle empty components list STARTED

constructor should handle empty components list PASSED

isTuple should return true for tuple types STARTED

isTuple should return true for tuple types PASSED

isTuple should return false for non-tuple types STARTED

isTuple should return false for non-tuple types PASSED

isTuple should handle null type STARTED

isTuple should handle null type PASSED

isTuple should handle empty type STARTED

isTuple should handle empty type PASSED

getters should return correct values STARTED

getters should return correct values PASSED

should handle null name STARTED

should handle null name PASSED

should handle null type in constructor STARTED

should handle null type in constructor PASSED

components should be immutable copy STARTED

components should be immutable copy PASSED

should handle complex nested components STARTED

should handle complex nested components PASSED

parseAbiContent should parse Truffle format ABI STARTED

parseAbiContent should parse Truffle format ABI PASSED

parseAbiContent should parse Hardhat format ABI STARTED

parseAbiContent should parse Hardhat format ABI PASSED

parseAbiContent should handle missing ABI section STARTED

parseAbiContent should handle missing ABI section PASSED

parseAbiContent should handle invalid JSON STARTED

parseAbiContent should handle invalid JSON PASSED

parseAbi should handle empty ABI JSON STARTED

parseAbi should handle empty ABI JSON PASSED

parseAbi should skip entries with invalid event types STARTED

parseAbi should skip entries with invalid event types PASSED

parseAbi should handle events with missing name STARTED

parseAbi should handle events with missing name PASSED

parseAbi should handle events with empty inputs STARTED

parseAbi should handle events with empty inputs PASSED

parseAbi should handle events with duplicate signatures STARTED

parseAbi should handle events with duplicate signatures PASSED

parseAbi should handle mixed case Solidity types STARTED

parseAbi should handle mixed case Solidity types PASSED

parseAbi should handle a large number of events STARTED

parseAbi should handle a large number of events PASSED

parseAbi should handle null or empty ABI content STARTED

parseAbi should handle null or empty ABI content PASSED

parseAbi should handle invalid JSON STARTED

parseAbi should handle invalid JSON PASSED

parseAbi should handle events with unsupported types STARTED

parseAbi should handle events with unsupported types PASSED

parseAbiContent should handle missing address in networks STARTED

parseAbiContent should handle missing address in networks PASSED

parseAbiContent should handle missing contract name in object key STARTED

parseAbiContent should handle missing contract name in object key PASSED

createTypeReference should handle unsupported types gracefully STARTED

createTypeReference should handle unsupported types gracefully PASSED

parseAbiContent should close input stream after parsing STARTED

parseAbiContent should close input stream after parsing PASSED

parseAbi should skip events with missing or empty names STARTED

parseAbi should skip events with missing or empty names PASSED

parseAbiContent should handle networksNode not being an object STARTED

parseAbiContent should handle networksNode not being an object PASSED

appendContractAddress should add new addresses only once STARTED

appendContractAddress should add new addresses only once PASSED

parseAbi should handle events with null inputs STARTED

parseAbi should handle events with null inputs PASSED

parseAbi should handle events with empty name STARTED

parseAbi should handle events with empty name PASSED

parseAbi should handle events with null name STARTED

parseAbi should handle events with null name PASSED

parseAbi should handle different Solidity types correctly STARTED

parseAbi should handle different Solidity types correctly PASSED

parseAbiContent should handle missing ABI section STARTED

parseAbiContent should handle missing ABI section PASSED

parseAbiContent should handle non-truffle format STARTED

parseAbiContent should handle non-truffle format PASSED

parseAbiContent should handle truffle format with multiple networks STARTED

parseAbiContent should handle truffle format with multiple networks PASSED

parseAndRegisterEvents should register events correctly STARTED

parseAndRegisterEvents should register events correctly PASSED

getABIEventByLog should find and return event for valid log STARTED

getABIEventByLog should find and return event for valid log PASSED

getABIEventByLog should throw exception when contract address not found STARTED

getABIEventByLog should throw exception when contract address not found PASSED

getABIEventByLog should throw exception when event signature not found STARTED

getABIEventByLog should throw exception when event signature not found PASSED

getABIEventByLog should handle case-insensitive matching STARTED

getABIEventByLog should handle case-insensitive matching PASSED

getContractAbiEventByLog should find and return contract ABI event for valid log STARTED

getContractAbiEventByLog should find and return contract ABI event for valid log PASSED

getContractAbiEventByLog should return null when contract address not found STARTED

getContractAbiEventByLog should return null when contract address not found PASSED

getContractAbiEventByLog should return null when event signature not found STARTED

getContractAbiEventByLog should return null when event signature not found PASSED

getContractAbiEventByLog should handle case-insensitive matching STARTED

getContractAbiEventByLog should handle case-insensitive matching PASSED

parseAbi should handle tuple types with components STARTED

parseAbi should handle tuple types with components PASSED

parseAbi should handle nested tuple types STARTED

parseAbi should handle nested tuple types PASSED

parseAbi should handle tuple array types STARTED

parseAbi should handle tuple array types PASSED

parseAbi should handle tuple with empty components STARTED

parseAbi should handle tuple with empty components PASSED

parseAbi should handle tuple with null components STARTED

parseAbi should handle tuple with null components PASSED

should convert Solidity type 'bool' with indexed=true to class org.web3j.abi.datatypes.Bool STARTED

should convert Solidity type 'bool' with indexed=true to class org.web3j.abi.datatypes.Bool PASSED

should convert Solidity type 'boolean' with indexed=true to class org.web3j.abi.datatypes.Bool STARTED

should convert Solidity type 'boolean' with indexed=true to class org.web3j.abi.datatypes.Bool PASSED

should convert Solidity type 'address' with indexed=false to class org.web3j.abi.datatypes.Address STARTED

should convert Solidity type 'address' with indexed=false to class org.web3j.abi.datatypes.Address PASSED

should convert Solidity type 'string' with indexed=false to class org.web3j.abi.datatypes.Utf8String STARTED

should convert Solidity type 'string' with indexed=false to class org.web3j.abi.datatypes.Utf8String PASSED

should convert Solidity type 'double' with indexed=false to class org.web3j.abi.datatypes.primitive.Double STARTED

should convert Solidity type 'double' with indexed=false to class org.web3j.abi.datatypes.primitive.Double PASSED

should convert Solidity type 'float' with indexed=false to class org.web3j.abi.datatypes.primitive.Float STARTED

should convert Solidity type 'float' with indexed=false to class org.web3j.abi.datatypes.primitive.Float PASSED

should convert Solidity type 'char' with indexed=false to class org.web3j.abi.datatypes.primitive.Char STARTED

should convert Solidity type 'char' with indexed=false to class org.web3j.abi.datatypes.primitive.Char PASSED

should convert Solidity type 'short' with indexed=false to class org.web3j.abi.datatypes.primitive.Short STARTED

should convert Solidity type 'short' with indexed=false to class org.web3j.abi.datatypes.primitive.Short PASSED

should convert Solidity type 'long' with indexed=false to class org.web3j.abi.datatypes.primitive.Long STARTED

should convert Solidity type 'long' with indexed=false to class org.web3j.abi.datatypes.primitive.Long PASSED

should convert Solidity type 'byte' with indexed=true to class org.web3j.abi.datatypes.primitive.Byte STARTED

should convert Solidity type 'byte' with indexed=true to class org.web3j.abi.datatypes.primitive.Byte PASSED

should convert Solidity type 'bytes' with indexed=false to class org.web3j.abi.datatypes.DynamicBytes STARTED

should convert Solidity type 'bytes' with indexed=false to class org.web3j.abi.datatypes.DynamicBytes PASSED

should convert Solidity type 'bytes1' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes1 STARTED

should convert Solidity type 'bytes1' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes1 PASSED

should convert Solidity type 'bytes2' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes2 STARTED

should convert Solidity type 'bytes2' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes2 PASSED

should convert Solidity type 'bytes3' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes3 STARTED

should convert Solidity type 'bytes3' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes3 PASSED

should convert Solidity type 'bytes4' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes4 STARTED

should convert Solidity type 'bytes4' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes4 PASSED

should convert Solidity type 'bytes5' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes5 STARTED

should convert Solidity type 'bytes5' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes5 PASSED

should convert Solidity type 'bytes6' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes6 STARTED

should convert Solidity type 'bytes6' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes6 PASSED

should convert Solidity type 'bytes7' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes7 STARTED

should convert Solidity type 'bytes7' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes7 PASSED

should convert Solidity type 'bytes8' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes8 STARTED

should convert Solidity type 'bytes8' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes8 PASSED

should convert Solidity type 'bytes9' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes9 STARTED

should convert Solidity type 'bytes9' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes9 PASSED

should convert Solidity type 'bytes10' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes10 STARTED

should convert Solidity type 'bytes10' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes10 PASSED

should convert Solidity type 'bytes11' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes11 STARTED

should convert Solidity type 'bytes11' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes11 PASSED

should convert Solidity type 'bytes12' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes12 STARTED

should convert Solidity type 'bytes12' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes12 PASSED

should convert Solidity type 'bytes13' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes13 STARTED

should convert Solidity type 'bytes13' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes13 PASSED

should convert Solidity type 'bytes14' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes14 STARTED

should convert Solidity type 'bytes14' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes14 PASSED

should convert Solidity type 'bytes15' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes15 STARTED

should convert Solidity type 'bytes15' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes15 PASSED

should convert Solidity type 'bytes16' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes16 STARTED

should convert Solidity type 'bytes16' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes16 PASSED

should convert Solidity type 'bytes17' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes17 STARTED

should convert Solidity type 'bytes17' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes17 PASSED

should convert Solidity type 'bytes18' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes18 STARTED

should convert Solidity type 'bytes18' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes18 PASSED

should convert Solidity type 'bytes19' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes19 STARTED

should convert Solidity type 'bytes19' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes19 PASSED

should convert Solidity type 'bytes20' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes20 STARTED

should convert Solidity type 'bytes20' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes20 PASSED

should convert Solidity type 'bytes21' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes21 STARTED

should convert Solidity type 'bytes21' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes21 PASSED

should convert Solidity type 'bytes22' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes22 STARTED

should convert Solidity type 'bytes22' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes22 PASSED

should convert Solidity type 'bytes23' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes23 STARTED

should convert Solidity type 'bytes23' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes23 PASSED

should convert Solidity type 'bytes24' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes24 STARTED

should convert Solidity type 'bytes24' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes24 PASSED

should convert Solidity type 'bytes25' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes25 STARTED

should convert Solidity type 'bytes25' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes25 PASSED

should convert Solidity type 'bytes26' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes26 STARTED

should convert Solidity type 'bytes26' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes26 PASSED

should convert Solidity type 'bytes27' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes27 STARTED

should convert Solidity type 'bytes27' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes27 PASSED

should convert Solidity type 'bytes28' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes28 STARTED

should convert Solidity type 'bytes28' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes28 PASSED

should convert Solidity type 'bytes29' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes29 STARTED

should convert Solidity type 'bytes29' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes29 PASSED

should convert Solidity type 'bytes30' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes30 STARTED

should convert Solidity type 'bytes30' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes30 PASSED

should convert Solidity type 'bytes31' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes31 STARTED

should convert Solidity type 'bytes31' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes31 PASSED

should convert Solidity type 'bytes32' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes32 STARTED

should convert Solidity type 'bytes32' with indexed=false to class org.web3j.abi.datatypes.generated.Bytes32 PASSED

should convert Solidity type 'uint' with indexed=false to class org.web3j.abi.datatypes.Uint STARTED

should convert Solidity type 'uint' with indexed=false to class org.web3j.abi.datatypes.Uint PASSED

should convert Solidity type 'uint8' with indexed=false to class org.web3j.abi.datatypes.generated.Uint8 STARTED

should convert Solidity type 'uint8' with indexed=false to class org.web3j.abi.datatypes.generated.Uint8 PASSED

should convert Solidity type 'uint16' with indexed=false to class org.web3j.abi.datatypes.generated.Uint16 STARTED

should convert Solidity type 'uint16' with indexed=false to class org.web3j.abi.datatypes.generated.Uint16 PASSED

should convert Solidity type 'uint24' with indexed=false to class org.web3j.abi.datatypes.generated.Uint24 STARTED

should convert Solidity type 'uint24' with indexed=false to class org.web3j.abi.datatypes.generated.Uint24 PASSED

should convert Solidity type 'uint32' with indexed=false to class org.web3j.abi.datatypes.generated.Uint32 STARTED

should convert Solidity type 'uint32' with indexed=false to class org.web3j.abi.datatypes.generated.Uint32 PASSED

should convert Solidity type 'uint40' with indexed=false to class org.web3j.abi.datatypes.generated.Uint40 STARTED

should convert Solidity type 'uint40' with indexed=false to class org.web3j.abi.datatypes.generated.Uint40 PASSED

should convert Solidity type 'uint48' with indexed=false to class org.web3j.abi.datatypes.generated.Uint48 STARTED

should convert Solidity type 'uint48' with indexed=false to class org.web3j.abi.datatypes.generated.Uint48 PASSED

should convert Solidity type 'uint56' with indexed=false to class org.web3j.abi.datatypes.generated.Uint56 STARTED

should convert Solidity type 'uint56' with indexed=false to class org.web3j.abi.datatypes.generated.Uint56 PASSED

should convert Solidity type 'uint64' with indexed=false to class org.web3j.abi.datatypes.generated.Uint64 STARTED

should convert Solidity type 'uint64' with indexed=false to class org.web3j.abi.datatypes.generated.Uint64 PASSED

should convert Solidity type 'uint72' with indexed=false to class org.web3j.abi.datatypes.generated.Uint72 STARTED

should convert Solidity type 'uint72' with indexed=false to class org.web3j.abi.datatypes.generated.Uint72 PASSED

should convert Solidity type 'uint80' with indexed=false to class org.web3j.abi.datatypes.generated.Uint80 STARTED

should convert Solidity type 'uint80' with indexed=false to class org.web3j.abi.datatypes.generated.Uint80 PASSED

should convert Solidity type 'uint88' with indexed=false to class org.web3j.abi.datatypes.generated.Uint88 STARTED

should convert Solidity type 'uint88' with indexed=false to class org.web3j.abi.datatypes.generated.Uint88 PASSED

should convert Solidity type 'uint96' with indexed=false to class org.web3j.abi.datatypes.generated.Uint96 STARTED

should convert Solidity type 'uint96' with indexed=false to class org.web3j.abi.datatypes.generated.Uint96 PASSED

should convert Solidity type 'uint104' with indexed=false to class org.web3j.abi.datatypes.generated.Uint104 STARTED

should convert Solidity type 'uint104' with indexed=false to class org.web3j.abi.datatypes.generated.Uint104 PASSED

should convert Solidity type 'uint112' with indexed=false to class org.web3j.abi.datatypes.generated.Uint112 STARTED

should convert Solidity type 'uint112' with indexed=false to class org.web3j.abi.datatypes.generated.Uint112 PASSED

should convert Solidity type 'uint120' with indexed=false to class org.web3j.abi.datatypes.generated.Uint120 STARTED

should convert Solidity type 'uint120' with indexed=false to class org.web3j.abi.datatypes.generated.Uint120 PASSED

should convert Solidity type 'uint128' with indexed=false to class org.web3j.abi.datatypes.generated.Uint128 STARTED

should convert Solidity type 'uint128' with indexed=false to class org.web3j.abi.datatypes.generated.Uint128 PASSED

should convert Solidity type 'uint136' with indexed=false to class org.web3j.abi.datatypes.generated.Uint136 STARTED

should convert Solidity type 'uint136' with indexed=false to class org.web3j.abi.datatypes.generated.Uint136 PASSED

should convert Solidity type 'uint144' with indexed=false to class org.web3j.abi.datatypes.generated.Uint144 STARTED

should convert Solidity type 'uint144' with indexed=false to class org.web3j.abi.datatypes.generated.Uint144 PASSED

should convert Solidity type 'uint152' with indexed=false to class org.web3j.abi.datatypes.generated.Uint152 STARTED

should convert Solidity type 'uint152' with indexed=false to class org.web3j.abi.datatypes.generated.Uint152 PASSED

should convert Solidity type 'uint160' with indexed=false to class org.web3j.abi.datatypes.generated.Uint160 STARTED

should convert Solidity type 'uint160' with indexed=false to class org.web3j.abi.datatypes.generated.Uint160 PASSED

should convert Solidity type 'uint168' with indexed=false to class org.web3j.abi.datatypes.generated.Uint168 STARTED

should convert Solidity type 'uint168' with indexed=false to class org.web3j.abi.datatypes.generated.Uint168 PASSED

should convert Solidity type 'uint176' with indexed=false to class org.web3j.abi.datatypes.generated.Uint176 STARTED

should convert Solidity type 'uint176' with indexed=false to class org.web3j.abi.datatypes.generated.Uint176 PASSED

should convert Solidity type 'uint184' with indexed=false to class org.web3j.abi.datatypes.generated.Uint184 STARTED

should convert Solidity type 'uint184' with indexed=false to class org.web3j.abi.datatypes.generated.Uint184 PASSED

should convert Solidity type 'uint192' with indexed=false to class org.web3j.abi.datatypes.generated.Uint192 STARTED

should convert Solidity type 'uint192' with indexed=false to class org.web3j.abi.datatypes.generated.Uint192 PASSED

should convert Solidity type 'uint200' with indexed=false to class org.web3j.abi.datatypes.generated.Uint200 STARTED

should convert Solidity type 'uint200' with indexed=false to class org.web3j.abi.datatypes.generated.Uint200 PASSED

should convert Solidity type 'uint208' with indexed=false to class org.web3j.abi.datatypes.generated.Uint208 STARTED

should convert Solidity type 'uint208' with indexed=false to class org.web3j.abi.datatypes.generated.Uint208 PASSED

should convert Solidity type 'uint216' with indexed=false to class org.web3j.abi.datatypes.generated.Uint216 STARTED

should convert Solidity type 'uint216' with indexed=false to class org.web3j.abi.datatypes.generated.Uint216 PASSED

should convert Solidity type 'uint224' with indexed=false to class org.web3j.abi.datatypes.generated.Uint224 STARTED

should convert Solidity type 'uint224' with indexed=false to class org.web3j.abi.datatypes.generated.Uint224 PASSED

should convert Solidity type 'uint232' with indexed=false to class org.web3j.abi.datatypes.generated.Uint232 STARTED

should convert Solidity type 'uint232' with indexed=false to class org.web3j.abi.datatypes.generated.Uint232 PASSED

should convert Solidity type 'uint240' with indexed=false to class org.web3j.abi.datatypes.generated.Uint240 STARTED

should convert Solidity type 'uint240' with indexed=false to class org.web3j.abi.datatypes.generated.Uint240 PASSED

should convert Solidity type 'uint248' with indexed=false to class org.web3j.abi.datatypes.generated.Uint248 STARTED

should convert Solidity type 'uint248' with indexed=false to class org.web3j.abi.datatypes.generated.Uint248 PASSED

should convert Solidity type 'uint256' with indexed=false to class org.web3j.abi.datatypes.generated.Uint256 STARTED

should convert Solidity type 'uint256' with indexed=false to class org.web3j.abi.datatypes.generated.Uint256 PASSED

should convert Solidity type 'int8' with indexed=false to class org.web3j.abi.datatypes.generated.Int8 STARTED

should convert Solidity type 'int8' with indexed=false to class org.web3j.abi.datatypes.generated.Int8 PASSED

should convert Solidity type 'int16' with indexed=false to class org.web3j.abi.datatypes.generated.Int16 STARTED

should convert Solidity type 'int16' with indexed=false to class org.web3j.abi.datatypes.generated.Int16 PASSED

should convert Solidity type 'int24' with indexed=false to class org.web3j.abi.datatypes.generated.Int24 STARTED

should convert Solidity type 'int24' with indexed=false to class org.web3j.abi.datatypes.generated.Int24 PASSED

should convert Solidity type 'int32' with indexed=false to class org.web3j.abi.datatypes.generated.Int32 STARTED

should convert Solidity type 'int32' with indexed=false to class org.web3j.abi.datatypes.generated.Int32 PASSED

should convert Solidity type 'int40' with indexed=false to class org.web3j.abi.datatypes.generated.Int40 STARTED

should convert Solidity type 'int40' with indexed=false to class org.web3j.abi.datatypes.generated.Int40 PASSED

should convert Solidity type 'int48' with indexed=false to class org.web3j.abi.datatypes.generated.Int48 STARTED

should convert Solidity type 'int48' with indexed=false to class org.web3j.abi.datatypes.generated.Int48 PASSED

should convert Solidity type 'int56' with indexed=false to class org.web3j.abi.datatypes.generated.Int56 STARTED

should convert Solidity type 'int56' with indexed=false to class org.web3j.abi.datatypes.generated.Int56 PASSED

should convert Solidity type 'int64' with indexed=false to class org.web3j.abi.datatypes.generated.Int64 STARTED

should convert Solidity type 'int64' with indexed=false to class org.web3j.abi.datatypes.generated.Int64 PASSED

should convert Solidity type 'int72' with indexed=false to class org.web3j.abi.datatypes.generated.Int72 STARTED

should convert Solidity type 'int72' with indexed=false to class org.web3j.abi.datatypes.generated.Int72 PASSED

should convert Solidity type 'int80' with indexed=false to class org.web3j.abi.datatypes.generated.Int80 STARTED

should convert Solidity type 'int80' with indexed=false to class org.web3j.abi.datatypes.generated.Int80 PASSED

should convert Solidity type 'int88' with indexed=false to class org.web3j.abi.datatypes.generated.Int88 STARTED

should convert Solidity type 'int88' with indexed=false to class org.web3j.abi.datatypes.generated.Int88 PASSED

should convert Solidity type 'int96' with indexed=false to class org.web3j.abi.datatypes.generated.Int96 STARTED

should convert Solidity type 'int96' with indexed=false to class org.web3j.abi.datatypes.generated.Int96 PASSED

should convert Solidity type 'int104' with indexed=false to class org.web3j.abi.datatypes.generated.Int104 STARTED

should convert Solidity type 'int104' with indexed=false to class org.web3j.abi.datatypes.generated.Int104 PASSED

should convert Solidity type 'int112' with indexed=false to class org.web3j.abi.datatypes.generated.Int112 STARTED

should convert Solidity type 'int112' with indexed=false to class org.web3j.abi.datatypes.generated.Int112 PASSED

should convert Solidity type 'int120' with indexed=false to class org.web3j.abi.datatypes.generated.Int120 STARTED

should convert Solidity type 'int120' with indexed=false to class org.web3j.abi.datatypes.generated.Int120 PASSED

should convert Solidity type 'int128' with indexed=false to class org.web3j.abi.datatypes.generated.Int128 STARTED

should convert Solidity type 'int128' with indexed=false to class org.web3j.abi.datatypes.generated.Int128 PASSED

should convert Solidity type 'int136' with indexed=false to class org.web3j.abi.datatypes.generated.Int136 STARTED

should convert Solidity type 'int136' with indexed=false to class org.web3j.abi.datatypes.generated.Int136 PASSED

should convert Solidity type 'int144' with indexed=false to class org.web3j.abi.datatypes.generated.Int144 STARTED

should convert Solidity type 'int144' with indexed=false to class org.web3j.abi.datatypes.generated.Int144 PASSED

should convert Solidity type 'int152' with indexed=false to class org.web3j.abi.datatypes.generated.Int152 STARTED

should convert Solidity type 'int152' with indexed=false to class org.web3j.abi.datatypes.generated.Int152 PASSED

should convert Solidity type 'int160' with indexed=false to class org.web3j.abi.datatypes.generated.Int160 STARTED

should convert Solidity type 'int160' with indexed=false to class org.web3j.abi.datatypes.generated.Int160 PASSED

should convert Solidity type 'int168' with indexed=false to class org.web3j.abi.datatypes.generated.Int168 STARTED

should convert Solidity type 'int168' with indexed=false to class org.web3j.abi.datatypes.generated.Int168 PASSED

should convert Solidity type 'int176' with indexed=false to class org.web3j.abi.datatypes.generated.Int176 STARTED

should convert Solidity type 'int176' with indexed=false to class org.web3j.abi.datatypes.generated.Int176 PASSED

should convert Solidity type 'int184' with indexed=false to class org.web3j.abi.datatypes.generated.Int184 STARTED

should convert Solidity type 'int184' with indexed=false to class org.web3j.abi.datatypes.generated.Int184 PASSED

should convert Solidity type 'int192' with indexed=false to class org.web3j.abi.datatypes.generated.Int192 STARTED

should convert Solidity type 'int192' with indexed=false to class org.web3j.abi.datatypes.generated.Int192 PASSED

should convert Solidity type 'int200' with indexed=false to class org.web3j.abi.datatypes.generated.Int200 STARTED

should convert Solidity type 'int200' with indexed=false to class org.web3j.abi.datatypes.generated.Int200 PASSED

should convert Solidity type 'int208' with indexed=false to class org.web3j.abi.datatypes.generated.Int208 STARTED

should convert Solidity type 'int208' with indexed=false to class org.web3j.abi.datatypes.generated.Int208 PASSED

should convert Solidity type 'int216' with indexed=false to class org.web3j.abi.datatypes.generated.Int216 STARTED

should convert Solidity type 'int216' with indexed=false to class org.web3j.abi.datatypes.generated.Int216 PASSED

should convert Solidity type 'int224' with indexed=false to class org.web3j.abi.datatypes.generated.Int224 STARTED

should convert Solidity type 'int224' with indexed=false to class org.web3j.abi.datatypes.generated.Int224 PASSED

should convert Solidity type 'int232' with indexed=false to class org.web3j.abi.datatypes.generated.Int232 STARTED

should convert Solidity type 'int232' with indexed=false to class org.web3j.abi.datatypes.generated.Int232 PASSED

should convert Solidity type 'int240' with indexed=false to class org.web3j.abi.datatypes.generated.Int240 STARTED

should convert Solidity type 'int240' with indexed=false to class org.web3j.abi.datatypes.generated.Int240 PASSED

should convert Solidity type 'int248' with indexed=false to class org.web3j.abi.datatypes.generated.Int248 STARTED

should convert Solidity type 'int248' with indexed=false to class org.web3j.abi.datatypes.generated.Int248 PASSED

should convert Solidity type 'int256' with indexed=false to class org.web3j.abi.datatypes.generated.Int256 STARTED

should convert Solidity type 'int256' with indexed=false to class org.web3j.abi.datatypes.generated.Int256 PASSED

should handle unsupported type gracefully STARTED

should handle unsupported type gracefully PASSED

should handle tuple type with components STARTED

should handle tuple type with components PASSED

should handle tuple array type with components STARTED

should handle tuple array type with components PASSED

should handle dynamic array type STARTED

should handle dynamic array type PASSED

should throw exception for unsupported array element type STARTED

should throw exception for unsupported array element type PASSED

should handle nested tuple components STARTED

should handle nested tuple components PASSED

should throw exception for unsupported nested component type STARTED

should throw exception for unsupported nested component type PASSED

constructor should be callable for completeness STARTED

constructor should be callable for completeness PASSED

should handle tuple type with null components STARTED

should handle tuple type with null components PASSED

should handle tuple type with empty components STARTED

should handle tuple type with empty components PASSED

should handle non-tuple type with null components STARTED

should handle non-tuple type with null components PASSED

should handle non-tuple type with components STARTED

should handle non-tuple type with components PASSED

should handle tuple type with null components in resolveComponentType STARTED

should handle tuple type with null components in resolveComponentType PASSED

should handle tuple type with empty components in resolveComponentType STARTED

should handle tuple type with empty components in resolveComponentType PASSED

should call getType method in dynamic array TypeReference STARTED

should call getType method in dynamic array TypeReference PASSED

constructor should set rawType and typeArguments STARTED

constructor should set rawType and typeArguments PASSED

constructor should handle single type argument STARTED

constructor should handle single type argument PASSED

constructor should handle no type arguments STARTED

constructor should handle no type arguments PASSED

getActualTypeArguments should return type arguments array STARTED

getActualTypeArguments should return type arguments array PASSED

getRawType should return raw type STARTED

getRawType should return raw type PASSED

getOwnerType should always return null STARTED

getOwnerType should always return null PASSED

should handle null raw type STARTED

should handle null raw type PASSED

should handle null type arguments STARTED

should handle null type arguments PASSED

should work with complex generic types STARTED

should work with complex generic types PASSED

should be usable as ParameterizedType interface STARTED

should be usable as ParameterizedType interface PASSED

should get object from S3 STARTED

should get object from S3 PASSED

should handle exception when getting object from S3 STARTED

should handle exception when getting object from S3 PASSED

should list common prefixes of objects STARTED

should list common prefixes of objects PASSED

should handle empty common prefixes STARTED

should handle empty common prefixes PASSED

should list objects with prefix STARTED

should list objects with prefix PASSED

should handle exception when listing objects STARTED

should handle exception when listing objects PASSED

should handle generic exception when getting object from S3 STARTED

should handle generic exception when getting object from S3 PASSED

should handle exception with message when getting object STARTED

should handle exception with message when getting object PASSED

should handle exception with message when listing common prefixes STARTED

should handle exception with message when listing common prefixes PASSED

should handle exception with message when listing objects STARTED

should handle exception with message when listing objects PASSED

generateStructClass should create StaticStruct for static types STARTED

generateStructClass should create StaticStruct for static types PASSED

generateStructClass should create DynamicStruct for dynamic types STARTED

generateStructClass should create DynamicStruct for dynamic types PASSED

generateStructClass should create DynamicStruct when DynamicBytes is present STARTED

generateStructClass should create DynamicStruct when DynamicBytes is present PASSED

generateStructClass should create DynamicStruct when DynamicArray is present STARTED

generateStructClass should create DynamicStruct when DynamicArray is present PASSED

generateStructClass should create DynamicStruct when DynamicStruct is present STARTED

generateStructClass should create DynamicStruct when DynamicStruct is present PASSED

generateStructClass should handle empty field types list STARTED

generateStructClass should handle empty field types list PASSED

generateStructClass should handle single field type STARTED

generateStructClass should handle single field type PASSED

generateStructClass should create unique class names STARTED

generateStructClass should create unique class names PASSED

isDynamic should return true for DynamicBytes STARTED

isDynamic should return true for DynamicBytes PASSED

isDynamic should return true for Utf8String STARTED

isDynamic should return true for Utf8String PASSED

isDynamic should return true for DynamicArray STARTED

isDynamic should return true for DynamicArray PASSED

isDynamic should return true for DynamicStruct STARTED

isDynamic should return true for DynamicStruct PASSED

isDynamic should return false for static types STARTED

isDynamic should return false for static types PASSED

isDynamic should handle null parameter STARTED

isDynamic should handle null parameter PASSED

generateStructClass should handle mixed static and dynamic types STARTED

generateStructClass should handle mixed static and dynamic types PASSED

generateStructClass should create constructor with correct parameters STARTED

generateStructClass should create constructor with correct parameters PASSED

constructor should be callable for completeness STARTED

constructor should be callable for completeness PASSED

should throw ConfigurationException when S3 bucket name is empty STARTED

should throw ConfigurationException when S3 bucket name is empty PASSED

should throw ConfigurationException when S3 bucket name is null STARTED

should throw ConfigurationException when S3 bucket name is null PASSED

should process multiple prefixes, objects and download json files STARTED

should process multiple prefixes, objects and download json files PASSED

should process single prefix, objects and download json files STARTED

should process single prefix, objects and download json files PASSED

should skip non-json files STARTED

should skip non-json files PASSED

should handle empty object list STARTED

should handle empty object list PASSED

should throw S3Exception when listing prefixes fails STARTED

should throw S3Exception when listing prefixes fails PASSED

should throw S3Exception when listing objects fails STARTED

should throw S3Exception when listing objects fails PASSED

should only process direct child objects STARTED

should only process direct child objects PASSED

should throw S3Exception when object retrieval fails STARTED

should throw S3Exception when object retrieval fails PASSED

should throw IOException when parsing fails STARTED

should throw IOException when parsing fails PASSED

should process single prefix, name start with dot STARTED

should process single prefix, name start with dot PASSED

execute should process one iteration and terminate when running is set to false STARTED

execute should process one iteration and terminate when running is set to false PASSED

execute should catch NumberFormatException and log error when checkInterval is invalid STARTED

execute should catch NumberFormatException and log error when checkInterval is invalid PASSED

execute should handle exceptions in monitoring loop STARTED

execute should handle exceptions in monitoring loop PASSED

execute should catch Exception in monitoring loop, log error, and continue execution STARTED

execute should catch Exception in monitoring loop, log error, and continue execution PASSED

monitorEvents should process block height and transactions STARTED

monitorEvents should process block height and transactions PASSED

monitorEvents should handle exceptions STARTED

monitorEvents should handle exceptions PASSED

monitorEvents should catch Exception, log error, and continue execution STARTED

monitorEvents should catch Exception, log error, and continue execution PASSED

monitorEvents should process new transactions when finalBlockHeight is valid STARTED

monitorEvents should process new transactions when finalBlockHeight is valid PASSED

processPendingTransactions should process transactions and update block height STARTED

processPendingTransactions should process transactions and update block height PASSED

processPendingTransactions should handle empty queue STARTED

processPendingTransactions should handle empty queue PASSED

processPendingTransactions should handle block height change STARTED

processPendingTransactions should handle block height change PASSED

processPendingTransactions should handle zero block height STARTED

processPendingTransactions should handle zero block height PASSED

processPendingTransactions should handle save failures STARTED

processPendingTransactions should handle save failures PASSED

processPendingTransactions should handle interrupted exception STARTED

processPendingTransactions should handle interrupted exception PASSED

processPendingTransactions should handle block height save failure at end of queue STARTED

processPendingTransactions should handle block height save failure at end of queue PASSED

processPendingTransactions should handle block height save failure on block change STARTED

processPendingTransactions should handle block height save failure on block change PASSED

processPendingTransactions should not save block height when consecutive transactions have same block height STARTED

processPendingTransactions should not save block height when consecutive transactions have same block height PASSED

saveTransaction should handle empty transaction hash STARTED

saveTransaction should handle empty transaction hash PASSED

saveTransaction should handle null transaction hash STARTED

saveTransaction should handle null transaction hash PASSED

saveTransaction should save events and block height STARTED

saveTransaction should save events and block height PASSED

saveTransaction should handle event save failure STARTED

saveTransaction should handle event save failure PASSED

saveTransaction should handle block height save failure STARTED

saveTransaction should handle block height save failure PASSED

fetchTraceId should handle valid JSON STARTED

fetchTraceId should handle valid JSON PASSED

fetchTraceId should handle empty traceId STARTED

fetchTraceId should handle empty traceId PASSED

fetchTraceId should handle null traceId STARTED

fetchTraceId should handle null traceId PASSED

fetchTraceId should handle JSON parsing exception STARTED

fetchTraceId should handle JSON parsing exception PASSED

fetchTraceId should skip zero bytes when building trace ID string STARTED

fetchTraceId should skip zero bytes when building trace ID string PASSED

fetchTraceId should handle JSON with unknown properties STARTED

fetchTraceId should handle JSON with unknown properties PASSED

processNewTransactions should process transactions successfully STARTED

processNewTransactions should process transactions successfully PASSED

processNewTransactions should process transactions is empty STARTED

processNewTransactions should process transactions is empty PASSED

processNewTransactions should exit when block height is zero STARTED

processNewTransactions should exit when block height is zero PASSED

processNewTransactions should exit when saveTransaction returns false STARTED

processNewTransactions should exit when saveTransaction returns false PASSED

processNewTransactions should handle InterruptedException STARTED

processNewTransactions should handle InterruptedException PASSED

processNewTransactions should exit loop when running is false STARTED

processNewTransactions should exit loop when running is false PASSED

processNewTransactions should exit early when saveTransaction returns false due to block height save failure STARTED

processNewTransactions should exit early when saveTransaction returns false due to block height save failure PASSED

savePendingTransaction should handle empty transaction hash STARTED

savePendingTransaction should handle empty transaction hash PASSED

savePendingTransaction should handle null transaction hash STARTED

savePendingTransaction should handle null transaction hash PASSED

savePendingTransactionBlockNumber should handle block height save failure STARTED

savePendingTransactionBlockNumber should handle block height save failure PASSED

monitorEvents should execute processNewTransactions when processPendingTransactions returns valid block height STARTED

monitorEvents should execute processNewTransactions when processPendingTransactions returns valid block height PASSED

monitorEvents should log error when exception occurs in monitoring process STARTED

monitorEvents should log error when exception occurs in monitoring process PASSED

monitorEvents should log error when exception escapes from executor task STARTED

monitorEvents should log error when exception escapes from executor task PASSED

monitorEvents should handle exceptions in processNewTransactions STARTED

monitorEvents should handle exceptions in processNewTransactions PASSED

monitorEvents should log error when exception occurs while getting filter logs STARTED

monitorEvents should log error when exception occurs while getting filter logs PASSED

monitorEvents should not call processNewTransactions when processPendingTransactions returns null STARTED

monitorEvents should not call processNewTransactions when processPendingTransactions returns null PASSED

monitorEvents should run without errors when all operations succeed STARTED

monitorEvents should run without errors when all operations succeed PASSED

savePendingTransaction should return false when eventRepository.save fails STARTED

savePendingTransaction should return false when eventRepository.save fails PASSED

processNewTransactions should handle null transaction from queue STARTED

processNewTransactions should handle null transaction from queue PASSED

processNewTransactions should handle websocket disconnection (block number -1) STARTED

processNewTransactions should handle websocket disconnection (block number -1) PASSED

processNewTransactions should handle saveTransaction failure STARTED

processNewTransactions should handle saveTransaction failure PASSED

sleep should handle InterruptedException STARTED

sleep should handle InterruptedException PASSED

saveTransaction should handle StructuredLogContext close exception STARTED

saveTransaction should handle StructuredLogContext close exception PASSED

savePendingTransaction should handle StructuredLogContext close exception STARTED

savePendingTransaction should handle StructuredLogContext close exception PASSED

processNewTransactions should continue when saveTransaction succeeds STARTED

processNewTransactions should continue when saveTransaction succeeds PASSED

saveTransaction should complete successfully with all operations STARTED

saveTransaction should complete successfully with all operations PASSED

savePendingTransaction should complete successfully with all operations STARTED

savePendingTransaction should complete successfully with all operations PASSED

saveTransaction should handle all edge cases in try-with-resources STARTED

saveTransaction should handle all edge cases in try-with-resources PASSED

savePendingTransaction should handle all edge cases in try-with-resources STARTED

savePendingTransaction should handle all edge cases in try-with-resources PASSED

monitorEvents should handle exception when getting block height STARTED

monitorEvents should handle exception when getting block height PASSED

saveTransaction should handle multiple events in transaction STARTED

saveTransaction should handle multiple events in transaction PASSED

savePendingTransaction should handle multiple events in transaction STARTED

savePendingTransaction should handle multiple events in transaction PASSED

saveTransaction should fail on first event when multiple events exist STARTED

saveTransaction should fail on first event when multiple events exist PASSED

savePendingTransaction should fail on second event when multiple events exist STARTED

savePendingTransaction should fail on second event when multiple events exist PASSED

processPendingTransactions should handle first transaction with non-zero block height STARTED

processPendingTransactions should handle first transaction with non-zero block height PASSED

saveTransaction should handle transaction with empty events list STARTED

saveTransaction should handle transaction with empty events list PASSED

savePendingTransaction should handle transaction with empty events list STARTED

savePendingTransaction should handle transaction with empty events list PASSED

saveTransaction should handle exception in try-with-resources close STARTED

saveTransaction should handle exception in try-with-resources close PASSED

savePendingTransaction should handle exception in try-with-resources close STARTED

savePendingTransaction should handle exception in try-with-resources close PASSED

> Task :testAdhoc

Should service start up successfully with command line runner and process ABI files STARTED

OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended

> Task :testAdhoc

Should service start up successfully with command line runner and process ABI files PASSED

Should processes ABI files from multiple zones STARTED

Should processes ABI files from multiple zones PASSED

Should correctly parses ABI files based on truffle environment variable STARTED

Should correctly parses ABI files based on truffle environment variable PASSED

Should skip non-json file STARTED

Should skip non-json file PASSED

Should skip deeply nested files and only process direct child objects STARTED

Should skip deeply nested files and only process direct child objects PASSED

Should start fails when parsing malformed JSON STARTED

Should start fails when parsing malformed JSON PASSED

Should start fails when ABI file lacks required abi section STARTED

Should start fails when ABI file lacks required abi section PASSED

Should start fails when s3 connect timeout STARTED

Should start fails when s3 connect timeout PASSED

Should fails to start when S3 bucket is inaccessible STARTED

Should fails to start when S3 bucket is inaccessible PASSED

Should use default values when optional env variables are missing STARTED

Should use default values when optional env variables are missing PASSED

Should load all configuration properties correctly STARTED

Should load all configuration properties correctly PASSED

Should load all configuration properties correctly when env is 'test' STARTED

Should load all configuration properties correctly when env is 'test' PASSED

Should handle invalid subscription check interval value gracefully STARTED

Should handle invalid subscription check interval value gracefully PASSED

Should handle invalid subscription allowable block timestamp diff sec value gracefully STARTED

Should handle invalid subscription allowable block timestamp diff sec value gracefully PASSED

Should load all configuration properties correctly when env is 'prod' STARTED

Should load all configuration properties correctly when env is 'prod' PASSED

Should successfully store parsed events from both new blocks and pending transactions to DynamoDB Events table STARTED

Should successfully store parsed events from both new blocks and pending transactions to DynamoDB Events table PASSED

Should updates block height correctly when processing pending transactions STARTED

Should updates block height correctly when processing pending transactions PASSED

Should updates block height correctly when processing new blocks STARTED

Should updates block height correctly when processing new blocks PASSED

Should handles multiple events from same block correctly when processing new blocks STARTED

Should handles multiple events from same block correctly when processing new blocks PASSED

Should handles multiple events from same transaction correctly when processing pending transactions STARTED

Should handles multiple events from same transaction correctly when processing pending transactions PASSED

Should save events but don't save block height when processing pending transactions with same block number STARTED

Should save events but don't save block height when processing pending transactions with same block number PASSED

Should save events and block height when processing new blocks STARTED

Should save events and block height when processing new blocks PASSED

Should connection pool managed correctly, operations complete successfully STARTED

Should connection pool managed correctly, operations complete successfully PASSED

Should handles transactions with empty events when processing new blocks STARTED

Should handles transactions with empty events when processing new blocks PASSED

Should log error and return false when EventDao save fail of new blocks using MockitoSpyBean STARTED

Should log error and return false when EventDao save fail of new blocks using MockitoSpyBean PASSED

Should log error and return false when EventDao save fail of pending transaction using MockitoSpyBean STARTED

Should log error and return false when EventDao save fail of pending transaction using MockitoSpyBean PASSED

Should log error and restart when BlockHeightDao get block fail using MockitoSpyBean STARTED

Should log error and restart when BlockHeightDao get block fail using MockitoSpyBean PASSED

Should log error and restart when BlockHeightDao save block height of new block fail using MockitoSpyBean STARTED

Should log error and restart when BlockHeightDao save block height of new block fail using MockitoSpyBean PASSED

Should log error and restart when BlockHeightDao save block height of pending transaction fail using MockitoSpyBean STARTED

Should log error and restart when BlockHeightDao save block height of pending transaction fail using MockitoSpyBean PASSED

Should restart when websocket disconnect STARTED

Should restart when websocket disconnect PASSED

Should continues operation when non-critical errors occur STARTED

Should continues operation when non-critical errors occur PASSED

Service retry when block number is zero of pending transaction STARTED

Service retry when block number is zero of pending transaction PASSED

Should retry when save block height of pending transaction error STARTED

Should retry when save block height of pending transaction error PASSED

Should retry when save event of pending transaction error STARTED

Should retry when save event of pending transaction error PASSED

Should retry when new transaction with transaction hash is null STARTED

Should retry when new transaction with transaction hash is null PASSED

Should retry when new transaction with block height is zero STARTED

Should retry when new transaction with block height is zero PASSED

Should retry when save event of new transaction error STARTED

Should retry when save event of new transaction error PASSED

Should fails to start when required ABI download failure STARTED

Should fails to start when required ABI download failure PASSED

Should restart when handle invalid subscription check interval value invalid STARTED

Should restart when handle invalid subscription check interval value invalid PASSED

Should detects and processes events from new blockchain blocks STARTED

Should detects and processes events from new blockchain blocks PASSED

Should process pending transactions from specified block height STARTED

Should process pending transactions from specified block height PASSED

Should event data correctly parsed into indexed and non-indexed values STARTED

Should event data correctly parsed into indexed and non-indexed values PASSED

Should extracts traceId from event non-indexed values when present STARTED

Should extracts traceId from event non-indexed values when present PASSED

Should run normally when timestamp is equal or less than allowable block timestamp different seconds STARTED

Should run normally when timestamp is equal or less than allowable block timestamp different seconds PASSED

Should event that don't contain traceId are processed successfully and logs with empty traceId in MDC STARTED

Should event that don't contain traceId are processed successfully and logs with empty traceId in MDC PASSED

Should run normally when timestamp against allowable block timestamp different seconds STARTED

Should run normally when timestamp against allowable block timestamp different seconds PASSED

Should handles events that don't match any loaded ABI definitions STARTED

Should handles events that don't match any loaded ABI definitions PASSED

Should handles websocket disconnect during active subscription STARTED

Should handles websocket disconnect during active subscription PASSED

Should handles websocket connection failure at startup STARTED

Should handles websocket connection failure at startup PASSED

Should rejects events with missing transaction hash in pending events STARTED

Should rejects events with missing transaction hash in pending events PASSED

Should rejects events with empty transaction hash STARTED

Should rejects events with empty transaction hash PASSED

Should rejects events with missing transaction hash in new blocks STARTED

Should rejects events with missing transaction hash in new blocks PASSED

Should rejects events with empty transaction hash in new blocks STARTED

Should rejects events with empty transaction hash in new blocks PASSED

Should rejects blocks with zero block number STARTED

Should rejects blocks with zero block number PASSED

Should convert type reference corresponding STARTED

Should convert type reference corresponding PASSED

Should processes events with tuple types correctly STARTED

Should processes events with tuple types correctly PASSED

Should processes events with tuple array types correctly STARTED

Should processes events with tuple array types correctly PASSED

Should processes events with nested tuple types correctly STARTED

Should processes events with nested tuple types correctly PASSED

Should processes events with dynamic array types correctly STARTED

Should processes events with dynamic array types correctly PASSED

Should log include relevant context fields in StructuredLoggingContext STARTED

Should log include relevant context fields in StructuredLoggingContext PASSED

Should appropriate log levels used for different scenarios STARTED

Should appropriate log levels used for different scenarios PASSED

Should start successfully with all dependencies available STARTED

Should start successfully with all dependencies available PASSED

Should automatically reinitialize monitoring if error occurs STARTED

Should automatically reinitialize monitoring if error occurs PASSED

Should start successfully with empty ABI bucket STARTED

Should start successfully with empty ABI bucket PASSED

Should start successfully with empty DynamoDB BlockHeight table STARTED

Should start successfully with empty DynamoDB BlockHeight table PASSED

Should fails to start when required properties are invalid STARTED

Should fails to start when required properties are invalid PASSED

Should restart if DynamoDB connection Error STARTED

Should restart if DynamoDB connection Error PASSED

{"@timestamp":"2025-07-16T01:45:20.986425+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.986703+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.987728+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.987758+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.988153+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.988187+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.988571+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.988591+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.98913+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.989154+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.989492+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.989526+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.989925+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.989942+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.990428+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.99045+07:00","@version":"1","message":"http-outgoing-230: Close connection","logger_name":"org.apache.http.impl.conn.DefaultManagedHttpClientConnection","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.99052+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.990844+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.990859+07:00","@version":"1","message":"http-outgoing-252: Close connection","logger_name":"org.apache.http.impl.conn.DefaultManagedHttpClientConnection","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-07-16T01:45:20.990885+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}

[Incubating] Problems report is available at: file:///Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java/build/reports/problems/problems-report.html

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 17m 18s
17 actionable tasks: 17 executed